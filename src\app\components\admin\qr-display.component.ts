import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { BarService } from '../../services/bar.service';
import { Bar } from '../../models/bar.model';

@Component({
  selector: 'app-qr-display',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatSnackBarModule
  ],
  template: `
    <mat-toolbar>
      <button mat-icon-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <span>{{ bar?.name }} - Código QR</span>
    </mat-toolbar>

    <div class="container" *ngIf="bar">
      <mat-card class="qr-card">
        <mat-card-header>
          <mat-card-title>Código QR para {{ bar.name }}</mat-card-title>
          <mat-card-subtitle>Los clientes pueden escanear este código para unirse a la cola</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="qr-container">
            <div class="qr-code" *ngIf="bar.qrCode">
              <img [src]="bar.qrCode" [alt]="'QR Code para ' + bar.name" />
            </div>
            
            <div class="qr-info">
              <p><strong>URL de la cola:</strong></p>
              <div class="url-container">
                <code>{{ queueUrl }}</code>
                <button mat-icon-button (click)="copyUrl()" matTooltip="Copiar URL">
                  <mat-icon>content_copy</mat-icon>
                </button>
              </div>
            </div>
            
            <div class="instructions">
              <h3>Instrucciones:</h3>
              <ol>
                <li>Imprime este código QR y colócalo en un lugar visible del bar</li>
                <li>Los clientes pueden escanearlo con su teléfono</li>
                <li>Serán redirigidos al formulario para unirse a la cola</li>
                <li>Recibirán su número de cola y podrán ver su estado</li>
              </ol>
            </div>
          </div>
        </mat-card-content>
        
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="downloadQR()">
            <mat-icon>download</mat-icon>
            Descargar QR
          </button>
          <button mat-button (click)="regenerateQR()">
            <mat-icon>refresh</mat-icon>
            Regenerar QR
          </button>
          <button mat-button (click)="printQR()">
            <mat-icon>print</mat-icon>
            Imprimir
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <div *ngIf="!bar && !isLoading" class="error-container">
      <mat-card>
        <mat-card-content>
          <p>Bar no encontrado</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .qr-card {
      max-width: 600px;
      margin: 20px auto;
    }
    
    .qr-container {
      text-align: center;
      padding: 20px 0;
    }
    
    .qr-code {
      margin: 20px 0;
    }
    
    .qr-code img {
      max-width: 300px;
      width: 100%;
      height: auto;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      background: white;
    }
    
    .qr-info {
      margin: 30px 0;
      text-align: left;
    }
    
    .url-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 10px 0;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    .url-container code {
      flex: 1;
      word-break: break-all;
      font-size: 0.9rem;
    }
    
    .instructions {
      margin: 30px 0;
      text-align: left;
    }
    
    .instructions h3 {
      color: #3f51b5;
      margin-bottom: 15px;
    }
    
    .instructions ol {
      padding-left: 20px;
    }
    
    .instructions li {
      margin: 8px 0;
      line-height: 1.5;
    }
    
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 50vh;
    }
    
    mat-card-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    @media print {
      mat-toolbar,
      mat-card-actions,
      .instructions {
        display: none !important;
      }
      
      .qr-card {
        box-shadow: none;
        border: none;
        margin: 0;
        max-width: none;
      }
      
      .qr-code img {
        max-width: 400px;
      }
    }
  `]
})
export class QrDisplayComponent implements OnInit {
  bar: Bar | null = null;
  queueUrl: string = '';
  isLoading = true;
  barId: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private barService: BarService,
    private snackBar: MatSnackBar
  ) {}

  async ngOnInit(): Promise<void> {
    this.barId = this.route.snapshot.paramMap.get('barId') || '';
    
    if (this.barId) {
      await this.loadBar();
      this.queueUrl = this.barService.getQueueUrl(this.barId);
    }
    
    this.isLoading = false;
  }

  private async loadBar(): Promise<void> {
    try {
      this.bar = await this.barService.getBar(this.barId);
      
      // Si no tiene QR code, generarlo
      if (this.bar && !this.bar.qrCode) {
        await this.generateQR();
      }
    } catch (error) {
      console.error('Error loading bar:', error);
    }
  }

  async copyUrl(): Promise<void> {
    try {
      await navigator.clipboard.writeText(this.queueUrl);
      this.snackBar.open('URL copiada al portapapeles', 'Cerrar', {
        duration: 2000
      });
    } catch (error) {
      console.error('Error copying URL:', error);
      this.snackBar.open('Error al copiar URL', 'Cerrar', {
        duration: 2000
      });
    }
  }

  async downloadQR(): Promise<void> {
    if (this.bar?.qrCode) {
      try {
        const link = document.createElement('a');
        link.download = `qr-${this.bar.name.replace(/\s+/g, '-').toLowerCase()}.png`;
        link.href = this.bar.qrCode;
        link.click();
      } catch (error) {
        console.error('Error downloading QR:', error);
        this.snackBar.open('Error al descargar QR', 'Cerrar', {
          duration: 2000
        });
      }
    }
  }

  async regenerateQR(): Promise<void> {
    if (this.barId) {
      try {
        await this.generateQR();
        this.snackBar.open('Código QR regenerado', 'Cerrar', {
          duration: 2000
        });
      } catch (error) {
        console.error('Error regenerating QR:', error);
        this.snackBar.open('Error al regenerar QR', 'Cerrar', {
          duration: 2000
        });
      }
    }
  }

  private async generateQR(): Promise<void> {
    const qrCode = await this.barService.generateQRCode(this.barId);
    if (this.bar) {
      this.bar.qrCode = qrCode;
    }
  }

  printQR(): void {
    window.print();
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }
}
