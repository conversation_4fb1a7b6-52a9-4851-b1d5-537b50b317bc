import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { AuthService } from '../../services/auth.service';
import { LoginRequest, RegisterRequest } from '../../models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTabsModule
  ],
  template: `
    <div class="center">
      <mat-card class="form-container">
        <mat-card-header>
          <mat-card-title>QueTeToca</mat-card-title>
          <mat-card-subtitle>Gestión de Colas para Bares</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <mat-tab-group>
            <!-- Tab de Login -->
            <mat-tab label="Iniciar Sesión">
              <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="auth-form">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                    El email es requerido
                  </mat-error>
                  <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                    Ingresa un email válido
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Contraseña</mat-label>
                  <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
                  <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                    La contraseña es requerida
                  </mat-error>
                </mat-form-field>

                <button
                  mat-raised-button
                  color="primary"
                  type="submit"
                  class="submit-btn"
                  [disabled]="loginForm.invalid || isLoading">
                  <mat-icon>login</mat-icon>
                  {{ isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión' }}
                </button>
              </form>
            </mat-tab>

            <!-- Tab de Registro -->
            <mat-tab label="Registrarse">
              <form [formGroup]="registerForm" (ngSubmit)="onRegister()" class="auth-form">
                <div class="name-row">
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Nombre</mat-label>
                    <input matInput formControlName="firstName" placeholder="Juan">
                    <mat-error *ngIf="registerForm.get('firstName')?.hasError('required')">
                      El nombre es requerido
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Apellido</mat-label>
                    <input matInput formControlName="lastName" placeholder="Pérez">
                    <mat-error *ngIf="registerForm.get('lastName')?.hasError('required')">
                      El apellido es requerido
                    </mat-error>
                  </mat-form-field>
                </div>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email</mat-label>
                  <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                    El email es requerido
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                    Ingresa un email válido
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Contraseña</mat-label>
                  <input matInput [type]="hideRegisterPassword ? 'password' : 'text'" formControlName="password">
                  <button mat-icon-button matSuffix (click)="hideRegisterPassword = !hideRegisterPassword" type="button">
                    <mat-icon>{{hideRegisterPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                    La contraseña es requerida
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                    La contraseña debe tener al menos 6 caracteres
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Confirmar Contraseña</mat-label>
                  <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword">
                  <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                    <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                    Confirma tu contraseña
                  </mat-error>
                  <mat-error *ngIf="registerForm.hasError('passwordMismatch')">
                    Las contraseñas no coinciden
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Nombre de la Empresa (Opcional)</mat-label>
                  <input matInput formControlName="companyName" placeholder="Mi Bar">
                  <mat-icon matSuffix>business</mat-icon>
                </mat-form-field>

                <button
                  mat-raised-button
                  color="primary"
                  type="submit"
                  class="submit-btn"
                  [disabled]="registerForm.invalid || isLoading">
                  <mat-icon>person_add</mat-icon>
                  {{ isLoading ? 'Registrando...' : 'Registrarse' }}
                </button>
              </form>
            </mat-tab>
          </mat-tab-group>

          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 500px;
      width: 100%;
    }

    .auth-form {
      padding: 20px 0;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .half-width {
      width: calc(50% - 8px);
      margin-bottom: 16px;
    }

    .name-row {
      display: flex;
      gap: 16px;
    }

    .submit-btn {
      width: 100%;
      padding: 12px;
      margin-top: 16px;
    }

    .submit-btn mat-icon {
      margin-right: 8px;
    }

    .error-message {
      color: #f44336;
      margin-top: 16px;
      padding: 12px;
      border: 1px solid #f44336;
      border-radius: 4px;
      background-color: #ffebee;
      text-align: center;
    }

    mat-card-title {
      font-size: 2rem;
      color: #3f51b5;
      text-align: center;
    }

    mat-card-subtitle {
      text-align: center;
      margin-bottom: 20px;
    }

    mat-tab-group {
      margin-top: 20px;
    }

    .center {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  registerForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  hidePassword = true;
  hideRegisterPassword = true;
  hideConfirmPassword = true;

  constructor(
    private authService: AuthService,
    private fb: FormBuilder
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });

    this.registerForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      companyName: ['']
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  async onLogin(): Promise<void> {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        const credentials: LoginRequest = this.loginForm.value;
        await this.authService.login(credentials);
      } catch (error: any) {
        this.errorMessage = error.message || 'Error al iniciar sesión. Verifica tus credenciales.';
        console.error('Login error:', error);
      } finally {
        this.isLoading = false;
      }
    }
  }

  async onRegister(): Promise<void> {
    if (this.registerForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        const userData: RegisterRequest = {
          email: this.registerForm.value.email,
          password: this.registerForm.value.password,
          firstName: this.registerForm.value.firstName,
          lastName: this.registerForm.value.lastName,
          companyName: this.registerForm.value.companyName || undefined
        };

        await this.authService.register(userData);
      } catch (error: any) {
        this.errorMessage = error.message || 'Error al registrarse. Por favor, inténtalo de nuevo.';
        console.error('Register error:', error);
      } finally {
        this.isLoading = false;
      }
    }
  }
}
