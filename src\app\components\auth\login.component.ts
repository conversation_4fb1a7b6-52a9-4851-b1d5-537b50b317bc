import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { AuthService } from '../../services/auth.service';
import { LoginRequest } from '../../models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule
  ],
  template: `
    <div class="center">
      <mat-card class="form-container">
        <mat-card-header>
          <mat-card-title>QueTeToca</mat-card-title>
          <mat-card-subtitle>Gestión de Colas para Bares</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="auth-form">
            <div class="login-header">
              <h3>Iniciar Sesión</h3>
              <p>Accede al panel de administración</p>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                El email es requerido
              </mat-error>
              <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                Ingresa un email válido
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Contraseña</mat-label>
              <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
              <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                La contraseña es requerida
              </mat-error>
            </mat-form-field>

            <button
              mat-raised-button
              color="primary"
              type="submit"
              class="submit-btn"
              [disabled]="loginForm.invalid || isLoading">
              <mat-icon>login</mat-icon>
              {{ isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión' }}
            </button>
          </form>

          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <div class="info-section">
            <mat-icon>info</mat-icon>
            <p>Solo los administradores pueden acceder al sistema. Si necesitas una cuenta, contacta al administrador principal.</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 500px;
      width: 100%;
    }

    .auth-form {
      padding: 20px 0;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .half-width {
      width: calc(50% - 8px);
      margin-bottom: 16px;
    }

    .name-row {
      display: flex;
      gap: 16px;
    }

    .submit-btn {
      width: 100%;
      padding: 12px;
      margin-top: 16px;
    }

    .submit-btn mat-icon {
      margin-right: 8px;
    }

    .error-message {
      color: #f44336;
      margin-top: 16px;
      padding: 12px;
      border: 1px solid #f44336;
      border-radius: 4px;
      background-color: #ffebee;
      text-align: center;
    }

    mat-card-title {
      font-size: 2rem;
      color: #3f51b5;
      text-align: center;
    }

    mat-card-subtitle {
      text-align: center;
      margin-bottom: 20px;
    }

    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .login-header h3 {
      margin: 0 0 10px 0;
      color: #3f51b5;
      font-size: 1.5rem;
    }

    .login-header p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }

    .info-section {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      margin-top: 30px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 8px;
      border-left: 4px solid #3f51b5;
    }

    .info-section mat-icon {
      color: #3f51b5;
      margin-top: 2px;
    }

    .info-section p {
      margin: 0;
      font-size: 0.9rem;
      color: #666;
      line-height: 1.4;
    }

    .center {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  hidePassword = true;

  constructor(
    private authService: AuthService,
    private fb: FormBuilder
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  async onLogin(): Promise<void> {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        const credentials: LoginRequest = this.loginForm.value;
        await this.authService.login(credentials);
      } catch (error: any) {
        this.errorMessage = error.message || 'Error al iniciar sesión. Verifica tus credenciales.';
        console.error('Login error:', error);
      } finally {
        this.isLoading = false;
      }
    }
  }
}
