import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="center">
      <mat-card class="form-container">
        <mat-card-header>
          <mat-card-title>QueTeToca</mat-card-title>
          <mat-card-subtitle>Gestión de Colas para Bares</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="login-content">
            <p>Inicia sesión para gestionar las colas de tus bares</p>
            
            <button 
              mat-raised-button 
              color="primary" 
              class="google-login-btn"
              (click)="signInWithGoogle()"
              [disabled]="isLoading">
              <mat-icon>account_circle</mat-icon>
              Iniciar sesión con Google
            </button>
            
            <div *ngIf="errorMessage" class="error-message">
              {{ errorMessage }}
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .login-content {
      text-align: center;
      padding: 20px 0;
    }
    
    .google-login-btn {
      width: 100%;
      margin: 20px 0;
      padding: 12px;
    }
    
    .google-login-btn mat-icon {
      margin-right: 8px;
    }
    
    .error-message {
      color: #f44336;
      margin-top: 16px;
      padding: 8px;
      border: 1px solid #f44336;
      border-radius: 4px;
      background-color: #ffebee;
    }
    
    mat-card-title {
      font-size: 2rem;
      color: #3f51b5;
      text-align: center;
    }
    
    mat-card-subtitle {
      text-align: center;
      margin-bottom: 20px;
    }
  `]
})
export class LoginComponent {
  isLoading = false;
  errorMessage = '';

  constructor(private authService: AuthService) {}

  async signInWithGoogle(): Promise<void> {
    this.isLoading = true;
    this.errorMessage = '';
    
    try {
      await this.authService.signInWithGoogle();
    } catch (error: any) {
      this.errorMessage = 'Error al iniciar sesión. Por favor, inténtalo de nuevo.';
      console.error('Login error:', error);
    } finally {
      this.isLoading = false;
    }
  }
}
