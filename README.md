# QueTeToca - Gestión de Colas para Bares

Una aplicación web desarrollada en Angular para gestionar colas en bares y restaurantes.

## Características

### Para Administradores
- **Autenticación con Google**: Login seguro para administradores
- **Gestión de Bares**: Crear y administrar múltiples bares
- **Panel de Control**: Vista en tiempo real de las colas
- **Gestión de Cola**: Llamar clientes, completar servicios, eliminar entradas
- **Códigos QR**: Generación automática de códigos QR para cada bar
- **Estadísticas**: Métricas en tiempo real de las colas

### Para Clientes
- **Acceso por QR**: Escanear código QR para acceder al formulario
- **Formulario Simple**: Nombre, teléfono y número de comensales
- **Estado en Tiempo Real**: Ver posición en cola y tiempo estimado
- **Notificaciones**: Saber cuándo la mesa está lista

## Tecnologías Utilizadas

- **Frontend**: Angular 17 con Material Design
- **Backend**: Firebase (Firestore + Authentication)
- **Autenticación**: Google OAuth
- **Base de Datos**: Cloud Firestore
- **Generación QR**: qrcode library
- **Hosting**: Firebase Hosting (recomendado)

## Instalación y Configuración

### Prerrequisitos
- Node.js (versión 18 o superior)
- npm o yarn
- Cuenta de Firebase

### Pasos de Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd QueTeToca
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar Firebase**
   - Crear un proyecto en [Firebase Console](https://console.firebase.google.com)
   - Habilitar Authentication con Google
   - Crear base de datos Firestore
   - Copiar la configuración de Firebase

4. **Configurar variables de entorno**
   - Editar `src/environments/environment.ts`
   - Editar `src/environments/environment.prod.ts`
   - Reemplazar los valores de configuración de Firebase

5. **Ejecutar en desarrollo**
   ```bash
   npm start
   ```

6. **Construir para producción**
   ```bash
   npm run build
   ```

## Estructura del Proyecto

```
src/
├── app/
│   ├── components/
│   │   ├── auth/           # Componentes de autenticación
│   │   ├── admin/          # Panel de administración
│   │   └── queue/          # Componentes de cola para usuarios
│   ├── services/           # Servicios de Angular
│   ├── models/             # Modelos de datos TypeScript
│   ├── guards/             # Guards de rutas
│   └── environments/       # Configuración de entornos
```

## Configuración de Firebase

### Firestore Collections

La aplicación utiliza las siguientes colecciones en Firestore:

- `users`: Información de usuarios administradores
- `companies`: Información de empresas
- `bars`: Información de bares
- `queue`: Entradas de cola

### Reglas de Seguridad de Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Bars can be read by anyone, written by authenticated users
    match /bars/{barId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Queue entries can be read/written by anyone (for public access)
    match /queue/{entryId} {
      allow read, write: if true;
    }
    
    // Companies can be read/written by authenticated users
    match /companies/{companyId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Uso de la Aplicación

### Para Administradores

1. **Iniciar Sesión**: Usar Google OAuth para autenticarse
2. **Crear Bar**: Agregar información del bar (nombre, dirección)
3. **Generar QR**: El sistema genera automáticamente un código QR
4. **Imprimir QR**: Colocar el código QR en el bar
5. **Gestionar Cola**: Monitorear y gestionar clientes en tiempo real

### Para Clientes

1. **Escanear QR**: Usar la cámara del teléfono para escanear
2. **Completar Formulario**: Ingresar nombre, teléfono y número de comensales
3. **Recibir Número**: Obtener número de cola y tiempo estimado
4. **Monitorear Estado**: Ver actualizaciones en tiempo real
5. **Recibir Notificación**: Saber cuándo la mesa está lista

## Despliegue

### Firebase Hosting

1. **Instalar Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Inicializar Firebase**
   ```bash
   firebase init hosting
   ```

3. **Construir y desplegar**
   ```bash
   npm run build
   firebase deploy
   ```

## Contribución

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## Soporte

Para soporte y preguntas, por favor crear un issue en el repositorio de GitHub.
