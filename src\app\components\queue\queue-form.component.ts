import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { QueueService } from '../../services/queue.service';
import { BarService } from '../../services/bar.service';
import { Bar } from '../../models/bar.model';

@Component({
  selector: 'app-queue-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="container">
      <mat-card class="form-container" *ngIf="bar">
        <mat-card-header>
          <mat-card-title>{{ bar.name }}</mat-card-title>
          <mat-card-subtitle>Únete a la cola</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="queueForm" (ngSubmit)="onSubmit()">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nombre</mat-label>
              <input matInput formControlName="customerName" placeholder="Tu nombre">
              <mat-error *ngIf="queueForm.get('customerName')?.hasError('required')">
                El nombre es requerido
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Teléfono</mat-label>
              <input matInput formControlName="phoneNumber" placeholder="Tu número de teléfono">
              <mat-error *ngIf="queueForm.get('phoneNumber')?.hasError('required')">
                El teléfono es requerido
              </mat-error>
              <mat-error *ngIf="queueForm.get('phoneNumber')?.hasError('pattern')">
                Formato de teléfono inválido
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Número de comensales</mat-label>
              <input matInput type="number" formControlName="partySize" placeholder="¿Cuántas personas?">
              <mat-error *ngIf="queueForm.get('partySize')?.hasError('required')">
                El número de comensales es requerido
              </mat-error>
              <mat-error *ngIf="queueForm.get('partySize')?.hasError('min')">
                Mínimo 1 comensal
              </mat-error>
            </mat-form-field>

            <button 
              mat-raised-button 
              color="primary" 
              type="submit" 
              class="submit-btn"
              [disabled]="queueForm.invalid || isSubmitting">
              <mat-icon>add</mat-icon>
              Unirse a la cola
            </button>
          </form>
        </mat-card-content>
      </mat-card>

      <div *ngIf="!bar && !isLoading" class="error-container">
        <mat-card>
          <mat-card-content>
            <p>Bar no encontrado</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .submit-btn {
      width: 100%;
      padding: 12px;
      margin-top: 16px;
    }
    
    .submit-btn mat-icon {
      margin-right: 8px;
    }
    
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 50vh;
    }
    
    mat-card-title {
      color: #3f51b5;
    }
  `]
})
export class QueueFormComponent implements OnInit {
  queueForm: FormGroup;
  bar: Bar | null = null;
  isLoading = true;
  isSubmitting = false;
  barId: string = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private queueService: QueueService,
    private barService: BarService,
    private snackBar: MatSnackBar
  ) {
    this.queueForm = this.fb.group({
      customerName: ['', [Validators.required]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      partySize: [1, [Validators.required, Validators.min(1)]]
    });
  }

  async ngOnInit(): Promise<void> {
    this.barId = this.route.snapshot.paramMap.get('barId') || '';
    
    if (this.barId) {
      try {
        this.bar = await this.barService.getBar(this.barId);
      } catch (error) {
        console.error('Error loading bar:', error);
      }
    }
    
    this.isLoading = false;
  }

  async onSubmit(): Promise<void> {
    if (this.queueForm.valid && this.barId) {
      this.isSubmitting = true;
      
      try {
        const formValue = this.queueForm.value;
        const queueEntry = await this.queueService.addToQueue(
          this.barId,
          formValue.customerName,
          formValue.phoneNumber,
          formValue.partySize
        );

        this.snackBar.open('¡Te has unido a la cola exitosamente!', 'Cerrar', {
          duration: 3000
        });

        // Redirigir a la página de estado de cola
        this.router.navigate(['/queue-status', queueEntry.id]);
        
      } catch (error) {
        console.error('Error joining queue:', error);
        this.snackBar.open('Error al unirse a la cola. Inténtalo de nuevo.', 'Cerrar', {
          duration: 3000
        });
      } finally {
        this.isSubmitting = false;
      }
    }
  }
}
