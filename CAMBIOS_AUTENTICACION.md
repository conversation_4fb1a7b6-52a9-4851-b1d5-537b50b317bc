# Cambios de Autenticación: Google OAuth → Usuario/Contraseña

## Resumen de cambios realizados

Se ha migrado completamente el sistema de autenticación de Google OAuth a un sistema tradicional de usuario y contraseña con JWT tokens.

## 📋 Archivos modificados

### 1. Modelos actualizados
- **`src/app/models/user.model.ts`**
  - Eliminado: `uid`, `displayName`, `photoURL`
  - Agregado: `id`, `firstName`, `lastName`, `isActive`, `createdAt`, `updatedAt`
  - Nuevos interfaces: `LoginRequest`, `RegisterRequest`, `AuthResponse`

### 2. Servicios completamente reescritos
- **`src/app/services/auth.service.ts`**
  - Eliminada dependencia de Firebase Auth
  - Implementado sistema JWT con refresh tokens
  - Métodos: `login()`, `register()`, `logout()`, `refreshToken()`
  - Gestión de tokens en localStorage

- **`src/app/services/api.service.ts`**
  - Eliminada dependencia de Firebase Auth
  - Implementado manejo de tokens JWT
  - Nuevos endpoints de autenticación

- **`src/app/services/bar.service.ts`**
  - Migrado completamente a API REST
  - Eliminadas todas las dependencias de Firestore

- **`src/app/services/queue.service.ts`**
  - Migrado completamente a API REST
  - Integración con WebSockets para tiempo real

### 3. Componentes actualizados
- **`src/app/components/auth/login.component.ts`**
  - Reescrito completamente con formularios reactivos
  - Pestañas separadas para login y registro
  - Validación de formularios con Angular Material
  - Campos: email, contraseña, nombre, apellido, empresa

- **`src/app/components/admin/admin-dashboard.component.ts`**
  - Actualizado para mostrar `firstName` y `lastName`
  - Cambio de `signOut()` a `logout()`

### 4. Interceptors nuevos
- **`src/app/interceptors/auth.interceptor.ts`**
  - Manejo automático de tokens JWT
  - Renovación automática de tokens expirados
  - Redirección en caso de tokens inválidos

- **`src/app/interceptors/error.interceptor.ts`**
  - Manejo centralizado de errores HTTP
  - Mensajes de error amigables para el usuario

### 5. Configuración actualizada
- **`src/app/app.config.ts`**
  - Eliminadas dependencias de Firebase
  - Agregados interceptors HTTP
  - Configuración de HttpClient

- **`src/environments/environment.ts`**
  - Eliminada configuración de Firebase
  - Mantenida configuración de API y WebSocket

- **`package.json`**
  - Eliminadas dependencias: `@angular/fire`, `firebase`
  - Mantenidas dependencias esenciales

## 🔧 Nuevas funcionalidades

### Sistema de autenticación JWT
- **Tokens de acceso** con expiración corta (15-30 min)
- **Refresh tokens** para renovación automática
- **Almacenamiento seguro** en localStorage
- **Renovación automática** transparente al usuario

### Formularios de autenticación
- **Login**: Email y contraseña
- **Registro**: Email, contraseña, nombre, apellido, empresa (opcional)
- **Validación en tiempo real** con mensajes de error
- **Confirmación de contraseña** en registro
- **Mostrar/ocultar contraseña**

### Manejo de errores mejorado
- **Interceptor de errores** centralizado
- **Mensajes amigables** para el usuario
- **Redirección automática** en sesión expirada
- **Notificaciones** con Angular Material Snackbar

## 🚀 Prompt actualizado para la API

El archivo `PROMPT_API_ACTUALIZADO.md` contiene el prompt completo para crear la API FastAPI con:

### Endpoints de autenticación:
- `POST /auth/register` - Registro de usuarios
- `POST /auth/login` - Inicio de sesión
- `POST /auth/logout` - Cierre de sesión
- `POST /auth/refresh` - Renovación de tokens
- `GET /auth/me` - Usuario actual

### Características de seguridad:
- **Hashing de contraseñas** con bcrypt
- **JWT tokens** con algoritmo HS256
- **Refresh tokens** para renovación
- **Rate limiting** para prevenir ataques
- **Validación de datos** con Pydantic

### Base de datos:
- **PostgreSQL** para producción
- **SQLite** para desarrollo
- **SQLAlchemy ORM** con migraciones Alembic
- **Modelos relacionales** optimizados

## 📱 Flujo de usuario actualizado

### Para administradores:
1. **Registro/Login** con email y contraseña
2. **Dashboard** con lista de bares
3. **Gestión de colas** en tiempo real
4. **Generación de códigos QR**

### Para clientes:
1. **Escanear QR** del bar
2. **Formulario** con datos personales
3. **Recibir número** de cola
4. **Monitorear estado** en tiempo real

## 🔄 Migración de datos

Si tienes datos existentes en Firebase:

### Usuarios:
```sql
-- Mapeo de campos Firebase → Nueva estructura
uid → id (generar nuevo ID)
email → email (mantener)
displayName → firstName + lastName (dividir)
photoURL → (eliminar, no usado)
```

### Bares y colas:
- Los datos se mantienen igual
- Solo cambia el método de acceso (API en lugar de Firestore directo)

## ⚡ Próximos pasos

1. **Implementar la API** usando el prompt en `PROMPT_API_ACTUALIZADO.md`
2. **Configurar base de datos** PostgreSQL o SQLite
3. **Probar autenticación** completa
4. **Migrar datos** existentes si es necesario
5. **Desplegar** en producción

## 🛠️ Comandos para desarrollo

```bash
# Instalar dependencias actualizadas
npm install

# Desarrollo con proxy a la API
npm start

# Desarrollo sin proxy
npm run start:no-proxy

# Compilar para producción
npm run build
```

## 📝 Notas importantes

- **Tokens en localStorage**: Considera usar httpOnly cookies para mayor seguridad en producción
- **HTTPS obligatorio**: Para tokens JWT en producción
- **Validación del lado del servidor**: Todos los datos deben validarse en la API
- **Rate limiting**: Implementar en endpoints de autenticación
- **Logs de seguridad**: Registrar intentos de login fallidos

La aplicación está ahora completamente independiente de Firebase para autenticación y lista para trabajar con cualquier API REST que implemente los endpoints especificados.
