import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { User, RegisterRequest } from '../../models/user.model';
import { CreateUserDialogComponent } from './create-user-dialog.component';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatTooltipModule
  ],
  template: `
    <mat-toolbar>
      <button mat-icon-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <span>Gestión de Usuarios</span>
      <span class="spacer"></span>
      <button mat-raised-button color="primary" (click)="openCreateUserDialog()">
        <mat-icon>person_add</mat-icon>
        Nuevo Usuario
      </button>
    </mat-toolbar>

    <div class="container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Usuarios del Sistema</mat-card-title>
          <mat-card-subtitle>Gestiona los usuarios que pueden acceder al sistema</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="users" class="users-table">
              <!-- Email Column -->
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let user">{{ user.email }}</td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Nombre</th>
                <td mat-cell *matCellDef="let user">{{ user.firstName }} {{ user.lastName }}</td>
              </ng-container>

              <!-- Role Column -->
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef>Rol</th>
                <td mat-cell *matCellDef="let user">
                  <span class="role-badge" [ngClass]="'role-' + user.role">
                    {{ user.role === 'admin' ? 'Administrador' : 'Usuario' }}
                  </span>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Estado</th>
                <td mat-cell *matCellDef="let user">
                  <span class="status-badge" [ngClass]="user.isActive ? 'status-active' : 'status-inactive'">
                    {{ user.isActive ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
              </ng-container>

              <!-- Created Column -->
              <ng-container matColumnDef="created">
                <th mat-header-cell *matHeaderCellDef>Creado</th>
                <td mat-cell *matCellDef="let user">{{ formatDate(user.createdAt) }}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Acciones</th>
                <td mat-cell *matCellDef="let user">
                  <button mat-icon-button (click)="editUser(user)" matTooltip="Editar">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button 
                    mat-icon-button 
                    (click)="toggleUserStatus(user)" 
                    [matTooltip]="user.isActive ? 'Desactivar' : 'Activar'">
                    <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>
                  </button>
                  <button 
                    mat-icon-button 
                    color="warn" 
                    (click)="deleteUser(user)" 
                    matTooltip="Eliminar"
                    [disabled]="user.role === 'admin'">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>

          <div *ngIf="users.length === 0" class="empty-state">
            <mat-icon class="empty-icon">people</mat-icon>
            <h3>No hay usuarios registrados</h3>
            <p>Crea el primer usuario para comenzar</p>
            <button mat-raised-button color="primary" (click)="openCreateUserDialog()">
              <mat-icon>person_add</mat-icon>
              Crear Usuario
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .spacer {
      flex: 1 1 auto;
    }
    
    .container {
      padding: 20px;
    }
    
    .table-container {
      overflow-x: auto;
      margin-top: 20px;
    }
    
    .users-table {
      width: 100%;
    }
    
    .role-badge, .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
    
    .role-admin {
      background-color: #e3f2fd;
      color: #1976d2;
    }
    
    .role-user {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }
    
    .status-active {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    
    .status-inactive {
      background-color: #ffebee;
      color: #c62828;
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
    }
    
    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    .empty-state h3 {
      color: #666;
      margin: 20px 0 10px 0;
    }
    
    .empty-state p {
      color: #999;
      margin-bottom: 30px;
    }
  `]
})
export class UserManagementComponent implements OnInit {
  users: User[] = [];
  displayedColumns: string[] = ['email', 'name', 'role', 'status', 'created', 'actions'];
  isLoading = false;

  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadUsers();
  }

  async loadUsers(): Promise<void> {
    try {
      this.isLoading = true;
      this.users = await firstValueFrom(this.apiService.getUsers()) || [];
    } catch (error) {
      console.error('Error loading users:', error);
      this.snackBar.open('Error al cargar usuarios', 'Cerrar', { duration: 3000 });
    } finally {
      this.isLoading = false;
    }
  }

  openCreateUserDialog(): void {
    const dialogRef = this.dialog.open(CreateUserDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
      }
    });
  }

  editUser(user: User): void {
    // Implementar edición de usuario
    console.log('Edit user:', user);
  }

  async toggleUserStatus(user: User): Promise<void> {
    try {
      const newStatus = !user.isActive;
      await firstValueFrom(this.apiService.updateUser(user.id, { isActive: newStatus }));

      this.snackBar.open(
        `Usuario ${newStatus ? 'activado' : 'desactivado'} correctamente`,
        'Cerrar',
        { duration: 3000 }
      );

      await this.loadUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
      this.snackBar.open('Error al actualizar estado del usuario', 'Cerrar', { duration: 3000 });
    }
  }

  async deleteUser(user: User): Promise<void> {
    if (confirm(`¿Estás seguro de que quieres eliminar al usuario ${user.firstName} ${user.lastName}?`)) {
      try {
        await firstValueFrom(this.apiService.deleteUser(user.id));
        this.snackBar.open('Usuario eliminado correctamente', 'Cerrar', { duration: 3000 });
        await this.loadUsers();
      } catch (error) {
        console.error('Error deleting user:', error);
        this.snackBar.open('Error al eliminar usuario', 'Cerrar', { duration: 3000 });
      }
    }
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('es-ES');
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }
}
