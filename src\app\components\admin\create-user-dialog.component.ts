import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { ApiService } from '../../services/api.service';
import { RegisterRequest } from '../../models/user.model';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-create-user-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <h2 mat-dialog-title>Crear Nuevo Usuario</h2>
    
    <mat-dialog-content>
      <form [formGroup]="userForm">
        <div class="name-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Nombre</mat-label>
            <input matInput formControlName="firstName" placeholder="Juan">
            <mat-error *ngIf="userForm.get('firstName')?.hasError('required')">
              El nombre es requerido
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Apellido</mat-label>
            <input matInput formControlName="lastName" placeholder="Pérez">
            <mat-error *ngIf="userForm.get('lastName')?.hasError('required')">
              El apellido es requerido
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="userForm.get('email')?.hasError('required')">
            El email es requerido
          </mat-error>
          <mat-error *ngIf="userForm.get('email')?.hasError('email')">
            Ingresa un email válido
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Contraseña</mat-label>
          <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
          <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="userForm.get('password')?.hasError('required')">
            La contraseña es requerida
          </mat-error>
          <mat-error *ngIf="userForm.get('password')?.hasError('minlength')">
            La contraseña debe tener al menos 6 caracteres
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Confirmar Contraseña</mat-label>
          <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword">
          <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
            <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="userForm.get('confirmPassword')?.hasError('required')">
            Confirma la contraseña
          </mat-error>
          <mat-error *ngIf="userForm.hasError('passwordMismatch')">
            Las contraseñas no coinciden
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Rol</mat-label>
          <mat-select formControlName="role">
            <mat-option value="admin">Administrador</mat-option>
            <mat-option value="user">Usuario</mat-option>
          </mat-select>
          <mat-error *ngIf="userForm.get('role')?.hasError('required')">
            Selecciona un rol
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nombre de la Empresa (Opcional)</mat-label>
          <input matInput formControlName="companyName" placeholder="Mi Empresa">
          <mat-icon matSuffix>business</mat-icon>
        </mat-form-field>
      </form>
    </mat-dialog-content>
    
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancelar</button>
      <button 
        mat-raised-button 
        color="primary" 
        (click)="onCreate()"
        [disabled]="userForm.invalid || isCreating">
        {{ isCreating ? 'Creando...' : 'Crear Usuario' }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .half-width {
      width: calc(50% - 8px);
      margin-bottom: 16px;
    }
    
    .name-row {
      display: flex;
      gap: 16px;
    }
    
    mat-dialog-content {
      min-width: 400px;
      padding: 20px 0;
    }
    
    mat-dialog-actions {
      padding: 16px 0;
    }
  `]
})
export class CreateUserDialogComponent {
  userForm: FormGroup;
  isCreating = false;
  hidePassword = true;
  hideConfirmPassword = true;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CreateUserDialogComponent>,
    private apiService: ApiService,
    private snackBar: MatSnackBar
  ) {
    this.userForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      role: ['user', [Validators.required]],
      companyName: ['']
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  async onCreate(): Promise<void> {
    if (this.userForm.valid) {
      this.isCreating = true;
      
      try {
        const userData: RegisterRequest = {
          email: this.userForm.value.email,
          password: this.userForm.value.password,
          firstName: this.userForm.value.firstName,
          lastName: this.userForm.value.lastName,
          companyName: this.userForm.value.companyName || undefined
        };
        
        await firstValueFrom(this.apiService.createUser(userData));

        this.snackBar.open('Usuario creado exitosamente', 'Cerrar', {
          duration: 3000
        });

        this.dialogRef.close(true);
        
      } catch (error: any) {
        console.error('Error creating user:', error);
        this.snackBar.open('Error al crear el usuario. Inténtalo de nuevo.', 'Cerrar', {
          duration: 3000
        });
      } finally {
        this.isCreating = false;
      }
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
