import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { AuthService } from '../../services/auth.service';
import { BarService } from '../../services/bar.service';
import { Bar } from '../../models/bar.model';
import { User } from '../../models/user.model';
import { CreateBarDialogComponent } from './create-bar-dialog.component';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatMenuModule,
    MatDialogModule
  ],
  template: `
    <mat-toolbar color="primary">
      <span>QueTeToca - Panel de Administración</span>
      <span class="spacer"></span>
      
      <button mat-icon-button [matMenuTriggerFor]="userMenu" *ngIf="currentUser">
        <mat-icon>account_circle</mat-icon>
      </button>

      <mat-menu #userMenu="matMenu">
        <div class="user-info">
          <p>{{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>
          <p class="user-email">{{ currentUser?.email }}</p>
        </div>
        <button mat-menu-item (click)="signOut()">
          <mat-icon>logout</mat-icon>
          Cerrar sesión
        </button>
      </mat-menu>
    </mat-toolbar>

    <div class="container">
      <div class="dashboard-header">
        <h1>Panel de Administración</h1>
        <div class="header-actions">
          <button mat-raised-button color="accent" (click)="manageUsers()">
            <mat-icon>people</mat-icon>
            Gestionar Usuarios
          </button>
          <button mat-raised-button color="primary" (click)="openCreateBarDialog()">
            <mat-icon>add</mat-icon>
            Nuevo Bar
          </button>
        </div>
      </div>

      <div class="section-header">
        <h2>Mis Bares</h2>
      </div>

      <div class="bars-grid" *ngIf="bars.length > 0">
        <mat-card class="bar-card" *ngFor="let bar of bars">
          <mat-card-header>
            <mat-card-title>{{ bar.name }}</mat-card-title>
            <mat-card-subtitle>{{ bar.address }}</mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <div class="bar-stats">
              <div class="stat">
                <mat-icon>people</mat-icon>
                <span>Cola actual: {{ getQueueCount(bar.id) }}</span>
              </div>
              <div class="stat">
                <mat-icon>schedule</mat-icon>
                <span>Tiempo promedio: 15 min</span>
              </div>
            </div>
          </mat-card-content>
          
          <mat-card-actions>
            <button mat-button (click)="manageQueue(bar.id)">
              <mat-icon>list</mat-icon>
              Gestionar Cola
            </button>
            <button mat-button (click)="showQRCode(bar)">
              <mat-icon>qr_code</mat-icon>
              Ver QR
            </button>
            <button mat-icon-button [matMenuTriggerFor]="barMenu">
              <mat-icon>more_vert</mat-icon>
            </button>
            
            <mat-menu #barMenu="matMenu">
              <button mat-menu-item (click)="editBar(bar)">
                <mat-icon>edit</mat-icon>
                Editar
              </button>
              <button mat-menu-item (click)="deleteBar(bar)" class="delete-option">
                <mat-icon>delete</mat-icon>
                Eliminar
              </button>
            </mat-menu>
          </mat-card-actions>
        </mat-card>
      </div>

      <div class="empty-state" *ngIf="bars.length === 0 && !isLoading">
        <mat-card>
          <mat-card-content>
            <div class="empty-content">
              <mat-icon class="empty-icon">store</mat-icon>
              <h2>No tienes bares registrados</h2>
              <p>Crea tu primer bar para comenzar a gestionar colas</p>
              <button mat-raised-button color="primary" (click)="openCreateBarDialog()">
                <mat-icon>add</mat-icon>
                Crear primer bar
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .spacer {
      flex: 1 1 auto;
    }
    
    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
    
    .user-info {
      padding: 16px;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .user-info p {
      margin: 4px 0;
    }
    
    .user-email {
      font-size: 0.9rem;
      color: #666;
    }
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px 0;
    }

    .dashboard-header h1 {
      margin: 0;
      color: #3f51b5;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .section-header {
      margin: 30px 0 20px 0;
    }

    .section-header h2 {
      margin: 0;
      color: #666;
      font-size: 1.5rem;
    }
    
    .bars-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .bar-card {
      transition: transform 0.2s ease-in-out;
    }
    
    .bar-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.12);
    }
    
    .bar-stats {
      margin: 16px 0;
    }
    
    .stat {
      display: flex;
      align-items: center;
      margin: 8px 0;
    }
    
    .stat mat-icon {
      margin-right: 8px;
      color: #666;
    }
    
    .empty-state {
      display: flex;
      justify-content: center;
      margin-top: 50px;
    }
    
    .empty-content {
      text-align: center;
      padding: 40px;
    }
    
    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    .empty-content h2 {
      color: #666;
      margin: 20px 0 10px 0;
    }
    
    .empty-content p {
      color: #999;
      margin-bottom: 30px;
    }
    
    .delete-option {
      color: #f44336;
    }
    
    mat-card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  `]
})
export class AdminDashboardComponent implements OnInit {
  currentUser: User | null = null;
  bars: Bar[] = [];
  isLoading = true;
  queueCounts: { [barId: string]: number } = {};

  constructor(
    private authService: AuthService,
    private barService: BarService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  async ngOnInit(): Promise<void> {
    this.authService.getCurrentUser().subscribe(user => {
      this.currentUser = user;
      if (user?.companyId) {
        this.loadBars(user.companyId);
      }
    });
  }

  async loadBars(companyId: string): Promise<void> {
    try {
      this.bars = await this.barService.getBarsForCompany(companyId);
      // Cargar estadísticas de cola para cada bar
      for (const bar of this.bars) {
        // Implementar carga de estadísticas
        this.queueCounts[bar.id] = 0; // Placeholder
      }
    } catch (error) {
      console.error('Error loading bars:', error);
    } finally {
      this.isLoading = false;
    }
  }

  openCreateBarDialog(): void {
    const dialogRef = this.dialog.open(CreateBarDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.currentUser?.companyId) {
        this.loadBars(this.currentUser.companyId);
      }
    });
  }

  manageQueue(barId: string): void {
    this.router.navigate(['/admin/queue', barId]);
  }

  manageUsers(): void {
    this.router.navigate(['/admin/users']);
  }

  showQRCode(bar: Bar): void {
    this.router.navigate(['/admin/qr', bar.id]);
  }

  editBar(bar: Bar): void {
    // Implementar edición de bar
    console.log('Edit bar:', bar);
  }

  deleteBar(bar: Bar): void {
    // Implementar eliminación de bar
    console.log('Delete bar:', bar);
  }

  getQueueCount(barId: string): number {
    return this.queueCounts[barId] || 0;
  }

  async signOut(): Promise<void> {
    await this.authService.logout();
  }
}
