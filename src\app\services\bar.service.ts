import { Injectable } from '@angular/core';
import { 
  Firestore, 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc,
  getDoc,
  Timestamp
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { Bar } from '../models/bar.model';
import * as QRCode from 'qrcode';

@Injectable({
  providedIn: 'root'
})
export class BarService {
  
  constructor(private firestore: Firestore) {}

  async createBar(name: string, address: string, companyId: string): Promise<Bar> {
    try {
      const barData: Omit<Bar, 'id'> = {
        name,
        address,
        companyId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const barsCollection = collection(this.firestore, 'bars');
      const docRef = await addDoc(barsCollection, {
        ...barData,
        createdAt: Timestamp.fromDate(barData.createdAt),
        updatedAt: Timestamp.fromDate(barData.updatedAt)
      });

      const newBar: Bar = {
        id: docRef.id,
        ...barData
      };

      // Generar código QR
      await this.generateQRCode(newBar.id);

      return newBar;
    } catch (error) {
      console.error('Error creating bar:', error);
      throw error;
    }
  }

  async getBarsForCompany(companyId: string): Promise<Bar[]> {
    try {
      const barsCollection = collection(this.firestore, 'bars');
      const q = query(barsCollection, where('companyId', '==', companyId));
      const snapshot = await getDocs(q);

      const bars: Bar[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        bars.push({
          id: doc.id,
          ...data,
          createdAt: data['createdAt'].toDate(),
          updatedAt: data['updatedAt'].toDate()
        } as Bar);
      });

      return bars;
    } catch (error) {
      console.error('Error getting bars for company:', error);
      throw error;
    }
  }

  async getBar(barId: string): Promise<Bar | null> {
    try {
      const barRef = doc(this.firestore, 'bars', barId);
      const barDoc = await getDoc(barRef);

      if (barDoc.exists()) {
        const data = barDoc.data();
        return {
          id: barDoc.id,
          ...data,
          createdAt: data['createdAt'].toDate(),
          updatedAt: data['updatedAt'].toDate()
        } as Bar;
      }

      return null;
    } catch (error) {
      console.error('Error getting bar:', error);
      throw error;
    }
  }

  async updateBar(barId: string, updates: Partial<Bar>): Promise<void> {
    try {
      const barRef = doc(this.firestore, 'bars', barId);
      await updateDoc(barRef, {
        ...updates,
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error updating bar:', error);
      throw error;
    }
  }

  async deleteBar(barId: string): Promise<void> {
    try {
      const barRef = doc(this.firestore, 'bars', barId);
      await deleteDoc(barRef);
    } catch (error) {
      console.error('Error deleting bar:', error);
      throw error;
    }
  }

  async generateQRCode(barId: string): Promise<string> {
    try {
      const queueUrl = `${window.location.origin}/queue/${barId}`;
      const qrCodeDataUrl = await QRCode.toDataURL(queueUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // Guardar el código QR en la base de datos
      const barRef = doc(this.firestore, 'bars', barId);
      await updateDoc(barRef, {
        qrCode: qrCodeDataUrl,
        updatedAt: Timestamp.fromDate(new Date())
      });

      return qrCodeDataUrl;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  }

  getQueueUrl(barId: string): string {
    return `${window.location.origin}/queue/${barId}`;
  }
}
