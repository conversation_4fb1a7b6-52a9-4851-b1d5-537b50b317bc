import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { Bar } from '../models/bar.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class BarService {

  constructor(private apiService: ApiService) {}

  async createBar(name: string, address: string, companyId: string): Promise<Bar> {
    try {
      const barData = {
        name,
        address,
        companyId
      };

      return this.apiService.createBar(barData).toPromise();
    } catch (error) {
      console.error('Error creating bar:', error);
      throw error;
    }
  }

  async getBarsForCompany(companyId: string): Promise<Bar[]> {
    try {
      const result = await firstValueFrom(this.apiService.getBars(companyId));
      return result || [];
    } catch (error) {
      console.error('Error getting bars for company:', error);
      throw error;
    }
  }

  async getBar(barId: string): Promise<Bar | null> {
    try {
      const result = await firstValueFrom(this.apiService.getBar(barId));
      return result || null;
    } catch (error) {
      console.error('Error getting bar:', error);
      throw error;
    }
  }

  async updateBar(barId: string, updates: Partial<Bar>): Promise<void> {
    try {
      await firstValueFrom(this.apiService.updateBar(barId, updates));
    } catch (error) {
      console.error('Error updating bar:', error);
      throw error;
    }
  }

  async deleteBar(barId: string): Promise<void> {
    try {
      await firstValueFrom(this.apiService.deleteBar(barId));
    } catch (error) {
      console.error('Error deleting bar:', error);
      throw error;
    }
  }

  async generateQRCode(barId: string): Promise<string> {
    try {
      const response = await firstValueFrom(this.apiService.generateQR(barId));
      return response?.qrCode || '';
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  }

  getQueueUrl(barId: string): string {
    return `${window.location.origin}/queue/${barId}`;
  }
}
