import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subscription, interval } from 'rxjs';
import { QueueService } from '../../services/queue.service';
import { QueueEntry, QueueStatus } from '../../models/queue-entry.model';

@Component({
  selector: 'app-queue-status',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="container">
      <mat-card class="status-card" *ngIf="queueEntry">
        <mat-card-header>
          <mat-card-title>Tu posición en la cola</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="status-content">
            <div class="queue-number">
              #{{ queueEntry.queueNumber }}
            </div>
            
            <div class="status-info">
              <div class="status-badge" [ngClass]="getStatusClass()">
                <mat-icon>{{ getStatusIcon() }}</mat-icon>
                {{ getStatusText() }}
              </div>
              
              <div class="customer-info">
                <p><strong>Nombre:</strong> {{ queueEntry.customerName }}</p>
                <p><strong>Comensales:</strong> {{ queueEntry.partySize }}</p>
                <p><strong>Teléfono:</strong> {{ queueEntry.phoneNumber }}</p>
              </div>
              
              <div class="time-info" *ngIf="queueEntry.status === 'waiting'">
                <p><strong>Tiempo estimado:</strong> {{ estimatedWaitTime }} minutos</p>
                <p><strong>Personas delante:</strong> {{ peopleAhead }}</p>
              </div>
              
              <div class="called-info" *ngIf="queueEntry.status === 'called'">
                <div class="alert-message">
                  <mat-icon>notification_important</mat-icon>
                  <span>¡Tu mesa está lista! Dirígete al mostrador.</span>
                </div>
              </div>
            </div>
            
            <div class="refresh-info">
              <p>Esta página se actualiza automáticamente cada 30 segundos</p>
              <button mat-button (click)="refreshStatus()">
                <mat-icon>refresh</mat-icon>
                Actualizar ahora
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <div *ngIf="!queueEntry && !isLoading" class="error-container">
        <mat-card>
          <mat-card-content>
            <p>No se encontró información de la cola</p>
          </mat-card-content>
        </mat-card>
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Cargando información...</p>
      </div>
    </div>
  `,
  styles: [`
    .status-card {
      max-width: 500px;
      margin: 20px auto;
    }
    
    .status-content {
      text-align: center;
      padding: 20px 0;
    }
    
    .queue-number {
      font-size: 4rem;
      font-weight: bold;
      color: #3f51b5;
      margin: 20px 0;
    }
    
    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 10px 20px;
      border-radius: 25px;
      font-weight: bold;
      margin: 20px 0;
    }
    
    .status-badge mat-icon {
      margin-right: 8px;
    }
    
    .status-waiting {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .status-called {
      background-color: #d4edda;
      color: #155724;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    .customer-info, .time-info {
      margin: 20px 0;
      text-align: left;
    }
    
    .customer-info p, .time-info p {
      margin: 8px 0;
    }
    
    .alert-message {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #4caf50;
      color: white;
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
      font-weight: bold;
    }
    
    .alert-message mat-icon {
      margin-right: 10px;
      font-size: 1.5rem;
    }
    
    .refresh-info {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
    }
    
    .refresh-info p {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 10px;
    }
    
    .loading-container, .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 50vh;
    }
    
    .loading-container p {
      margin-top: 20px;
    }
  `]
})
export class QueueStatusComponent implements OnInit, OnDestroy {
  queueEntry: QueueEntry | null = null;
  isLoading = true;
  peopleAhead = 0;
  estimatedWaitTime = 0;
  private refreshSubscription?: Subscription;
  private queueEntryId: string = '';

  constructor(
    private route: ActivatedRoute,
    private queueService: QueueService
  ) {}

  async ngOnInit(): Promise<void> {
    this.queueEntryId = this.route.snapshot.paramMap.get('entryId') || '';
    
    if (this.queueEntryId) {
      await this.loadQueueEntry();
      this.startAutoRefresh();
    } else {
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  private async loadQueueEntry(): Promise<void> {
    // Implementar lógica para cargar entrada específica de la cola
    // Por ahora, simulamos la carga
    this.isLoading = false;
  }

  private startAutoRefresh(): void {
    // Actualizar cada 30 segundos
    this.refreshSubscription = interval(30000).subscribe(() => {
      this.refreshStatus();
    });
  }

  async refreshStatus(): Promise<void> {
    if (this.queueEntryId) {
      await this.loadQueueEntry();
    }
  }

  getStatusClass(): string {
    if (!this.queueEntry) return '';
    
    switch (this.queueEntry.status) {
      case QueueStatus.WAITING:
        return 'status-waiting';
      case QueueStatus.CALLED:
        return 'status-called';
      default:
        return '';
    }
  }

  getStatusIcon(): string {
    if (!this.queueEntry) return 'help';
    
    switch (this.queueEntry.status) {
      case QueueStatus.WAITING:
        return 'schedule';
      case QueueStatus.CALLED:
        return 'notifications_active';
      default:
        return 'help';
    }
  }

  getStatusText(): string {
    if (!this.queueEntry) return '';
    
    switch (this.queueEntry.status) {
      case QueueStatus.WAITING:
        return 'Esperando';
      case QueueStatus.CALLED:
        return '¡Mesa lista!';
      default:
        return 'Estado desconocido';
    }
  }
}
