import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, of, firstValueFrom } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private router: Router,
    private apiService: ApiService
  ) {
    // Verificar si hay un token guardado al inicializar
    this.checkStoredToken();
  }

  private checkStoredToken(): void {
    const token = localStorage.getItem('auth_token');
    if (token) {
      // Verificar si el token es válido
      this.apiService.getCurrentUser().subscribe({
        next: (user) => {
          this.currentUserSubject.next(user);
        },
        error: () => {
          // Token inválido, limpiar storage
          this.clearAuthData();
        }
      });
    }
  }

  async login(credentials: LoginRequest): Promise<void> {
    try {
      const response = await firstValueFrom(this.apiService.login(credentials));
      if (response) {
        this.handleAuthSuccess(response);
        this.router.navigate(['/admin']);
      }
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<void> {
    try {
      const response = await firstValueFrom(this.apiService.register(userData));
      if (response) {
        this.handleAuthSuccess(response);
        this.router.navigate(['/admin']);
      }
    } catch (error) {
      console.error('Error during registration:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      // Llamar al endpoint de logout si existe
      await firstValueFrom(this.apiService.logout()).catch(() => {
        // Ignorar errores del logout en el servidor
      });
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      this.clearAuthData();
      this.router.navigate(['/login']);
    }
  }

  private handleAuthSuccess(response: AuthResponse): void {
    // Guardar tokens en localStorage
    localStorage.setItem('auth_token', response.token);
    localStorage.setItem('refresh_token', response.refreshToken);

    // Actualizar usuario actual
    this.currentUserSubject.next(response.user);
  }

  private clearAuthData(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    this.currentUserSubject.next(null);
  }

  getCurrentUser(): Observable<User | null> {
    return this.currentUser$;
  }

  isAuthenticated(): Observable<boolean> {
    return this.currentUser$.pipe(map(user => !!user));
  }

  isAdmin(): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => user?.role === 'admin')
    );
  }

  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  async refreshToken(): Promise<void> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await firstValueFrom(this.apiService.refreshToken(refreshToken));
      if (response) {
        this.handleAuthSuccess(response);
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      this.clearAuthData();
      this.router.navigate(['/login']);
      throw error;
    }
  }
}
