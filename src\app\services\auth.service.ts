import { Injectable } from '@angular/core';
import { Auth, GoogleAuthProvider, signInWithPopup, signOut, user } from '@angular/fire/auth';
import { Firestore, doc, getDoc, setDoc } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { Observable, map, switchMap, of } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  user$ = user(this.auth);
  
  constructor(
    private auth: Auth,
    private firestore: Firestore,
    private router: Router
  ) {}

  async signInWithGoogle(): Promise<void> {
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(this.auth, provider);
      
      if (result.user) {
        await this.createOrUpdateUser(result.user);
        this.router.navigate(['/admin']);
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      await signOut(this.auth);
      this.router.navigate(['/login']);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  private async createOrUpdateUser(firebaseUser: any): Promise<void> {
    const userRef = doc(this.firestore, `users/${firebaseUser.uid}`);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      const newUser: User = {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        role: 'admin' // Por defecto, los usuarios que se registran son admins
      };
      
      await setDoc(userRef, newUser);
    }
  }

  getCurrentUser(): Observable<User | null> {
    return this.user$.pipe(
      switchMap(firebaseUser => {
        if (!firebaseUser) return of(null);
        
        const userRef = doc(this.firestore, `users/${firebaseUser.uid}`);
        return getDoc(userRef).then(doc => {
          if (doc.exists()) {
            return doc.data() as User;
          }
          return null;
        });
      })
    );
  }

  isAuthenticated(): Observable<boolean> {
    return this.user$.pipe(map(user => !!user));
  }

  isAdmin(): Observable<boolean> {
    return this.getCurrentUser().pipe(
      map(user => user?.role === 'admin')
    );
  }
}
