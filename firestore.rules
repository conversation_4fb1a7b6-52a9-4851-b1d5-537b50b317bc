rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Bars can be read by anyone, written by authenticated users
    match /bars/{barId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Queue entries can be read/written by anyone (for public access)
    match /queue/{entryId} {
      allow read, write: if true;
    }
    
    // Companies can be read/written by authenticated users
    match /companies/{companyId} {
      allow read, write: if request.auth != null;
    }
  }
}
