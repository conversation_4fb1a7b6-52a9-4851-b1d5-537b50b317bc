import { Injectable } from '@angular/core';
import { Observable, firstValueFrom } from 'rxjs';
import { QueueEntry, QueueStatus, QueueStats } from '../models/queue-entry.model';
import { ApiService } from './api.service';
import { WebSocketService } from './websocket.service';

@Injectable({
  providedIn: 'root'
})
export class QueueService {

  constructor(
    private apiService: ApiService,
    private webSocketService: WebSocketService
  ) {}

  async addToQueue(barId: string, customerName: string, phoneNumber: string, partySize: number): Promise<QueueEntry> {
    try {
      const queueData = {
        customerName,
        phoneNumber,
        partySize
      };

      const result = await firstValueFrom(this.apiService.joinQueue(barId, queueData));
      return result;
    } catch (error) {
      console.error('Error adding to queue:', error);
      throw error;
    }
  }

  getQueueForBar(barId: string): Observable<QueueEntry[]> {
    // Usar WebSocket para actualizaciones en tiempo real
    return this.webSocketService.subscribeToQueueUpdates(barId);
  }

  async callCustomer(queueEntryId: string): Promise<void> {
    try {
      await firstValueFrom(this.apiService.callCustomer(queueEntryId));
    } catch (error) {
      console.error('Error calling customer:', error);
      throw error;
    }
  }

  async completeEntry(queueEntryId: string): Promise<void> {
    try {
      await firstValueFrom(this.apiService.completeEntry(queueEntryId));
    } catch (error) {
      console.error('Error completing entry:', error);
      throw error;
    }
  }

  async removeFromQueue(queueEntryId: string): Promise<void> {
    try {
      await firstValueFrom(this.apiService.removeFromQueue(queueEntryId));
    } catch (error) {
      console.error('Error removing from queue:', error);
      throw error;
    }
  }

  async getQueueStats(barId: string): Promise<QueueStats> {
    try {
      const result = await firstValueFrom(this.apiService.getBarStats(barId));
      return result;
    } catch (error) {
      console.error('Error getting queue stats:', error);
      throw error;
    }
  }
}
