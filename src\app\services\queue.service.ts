import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { QueueEntry, QueueStatus, QueueStats } from '../models/queue-entry.model';
import { ApiService } from './api.service';
import { WebSocketService } from './websocket.service';

@Injectable({
  providedIn: 'root'
})
export class QueueService {

  constructor(
    private apiService: ApiService,
    private webSocketService: WebSocketService
  ) {}

  async addToQueue(barId: string, customerName: string, phoneNumber: string, partySize: number): Promise<QueueEntry> {
    try {
      const queueData = {
        customerName,
        phoneNumber,
        partySize
      };

      return this.apiService.joinQueue(barId, queueData).toPromise();
    } catch (error) {
      console.error('Error adding to queue:', error);
      throw error;
    }
  }

  getQueueForBar(barId: string): Observable<QueueEntry[]> {
    // Usar WebSocket para actualizaciones en tiempo real
    return this.webSocketService.subscribeToQueueUpdates(barId);
  }

  async callCustomer(queueEntryId: string): Promise<void> {
    try {
      await this.apiService.callCustomer(queueEntryId).toPromise();
    } catch (error) {
      console.error('Error calling customer:', error);
      throw error;
    }
  }

  async completeEntry(queueEntryId: string): Promise<void> {
    try {
      await this.apiService.completeEntry(queueEntryId).toPromise();
    } catch (error) {
      console.error('Error completing entry:', error);
      throw error;
    }
  }

  async removeFromQueue(queueEntryId: string): Promise<void> {
    try {
      await this.apiService.removeFromQueue(queueEntryId).toPromise();
    } catch (error) {
      console.error('Error removing from queue:', error);
      throw error;
    }
  }

  async getQueueStats(barId: string): Promise<QueueStats> {
    try {
      const queueCollection = collection(this.firestore, 'queue');
      const waitingQuery = query(
        queueCollection,
        where('barId', '==', barId),
        where('status', '==', QueueStatus.WAITING)
      );
      
      const calledQuery = query(
        queueCollection,
        where('barId', '==', barId),
        where('status', '==', QueueStatus.CALLED)
      );

      const [waitingSnapshot, calledSnapshot] = await Promise.all([
        getDocs(waitingQuery),
        getDocs(calledQuery)
      ]);

      const totalWaiting = waitingSnapshot.size;
      const totalCalled = calledSnapshot.size;

      // Obtener el número actual más alto
      const allQuery = query(
        queueCollection,
        where('barId', '==', barId),
        orderBy('queueNumber', 'desc')
      );
      
      const allSnapshot = await getDocs(allQuery);
      const currentNumber = allSnapshot.empty ? 0 : allSnapshot.docs[0].data()['queueNumber'];

      return {
        totalWaiting,
        totalCalled,
        averageWaitTime: 15, // Placeholder - calcular basado en datos históricos
        currentNumber
      };
    } catch (error) {
      console.error('Error getting queue stats:', error);
      throw error;
    }
  }

  private async getNextQueueNumber(barId: string): Promise<number> {
    try {
      const queueCollection = collection(this.firestore, 'queue');
      const q = query(
        queueCollection,
        where('barId', '==', barId),
        orderBy('queueNumber', 'desc')
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return 1;
      }

      const lastEntry = snapshot.docs[0].data();
      return lastEntry['queueNumber'] + 1;
    } catch (error) {
      console.error('Error getting next queue number:', error);
      return 1;
    }
  }
}
