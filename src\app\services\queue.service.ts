import { Injectable } from '@angular/core';
import { 
  Firestore, 
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  doc, 
  updateDoc, 
  deleteDoc,
  getDocs,
  Timestamp
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { QueueEntry, QueueStatus, QueueStats } from '../models/queue-entry.model';

@Injectable({
  providedIn: 'root'
})
export class QueueService {
  
  constructor(private firestore: Firestore) {}

  async addToQueue(barId: string, customerName: string, phoneNumber: string, partySize: number): Promise<QueueEntry> {
    try {
      // Obtener el siguiente número de cola
      const queueNumber = await this.getNextQueueNumber(barId);
      
      const queueEntry: Omit<QueueEntry, 'id'> = {
        barId,
        customerName,
        phoneNumber,
        partySize,
        queueNumber,
        status: QueueStatus.WAITING,
        createdAt: new Date()
      };

      const queueCollection = collection(this.firestore, 'queue');
      const docRef = await addDoc(queueCollection, {
        ...queueEntry,
        createdAt: Timestamp.fromDate(queueEntry.createdAt)
      });

      return {
        id: docRef.id,
        ...queueEntry
      };
    } catch (error) {
      console.error('Error adding to queue:', error);
      throw error;
    }
  }

  getQueueForBar(barId: string): Observable<QueueEntry[]> {
    const queueCollection = collection(this.firestore, 'queue');
    const q = query(
      queueCollection,
      where('barId', '==', barId),
      where('status', 'in', [QueueStatus.WAITING, QueueStatus.CALLED]),
      orderBy('queueNumber', 'asc')
    );

    return new Observable(observer => {
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const entries: QueueEntry[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          entries.push({
            id: doc.id,
            ...data,
            createdAt: data['createdAt'].toDate(),
            calledAt: data['calledAt']?.toDate(),
            completedAt: data['completedAt']?.toDate()
          } as QueueEntry);
        });
        observer.next(entries);
      });

      return () => unsubscribe();
    });
  }

  async callCustomer(queueEntryId: string): Promise<void> {
    try {
      const queueRef = doc(this.firestore, 'queue', queueEntryId);
      await updateDoc(queueRef, {
        status: QueueStatus.CALLED,
        calledAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error calling customer:', error);
      throw error;
    }
  }

  async completeEntry(queueEntryId: string): Promise<void> {
    try {
      const queueRef = doc(this.firestore, 'queue', queueEntryId);
      await updateDoc(queueRef, {
        status: QueueStatus.COMPLETED,
        completedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error completing entry:', error);
      throw error;
    }
  }

  async removeFromQueue(queueEntryId: string): Promise<void> {
    try {
      const queueRef = doc(this.firestore, 'queue', queueEntryId);
      await deleteDoc(queueRef);
    } catch (error) {
      console.error('Error removing from queue:', error);
      throw error;
    }
  }

  async getQueueStats(barId: string): Promise<QueueStats> {
    try {
      const queueCollection = collection(this.firestore, 'queue');
      const waitingQuery = query(
        queueCollection,
        where('barId', '==', barId),
        where('status', '==', QueueStatus.WAITING)
      );
      
      const calledQuery = query(
        queueCollection,
        where('barId', '==', barId),
        where('status', '==', QueueStatus.CALLED)
      );

      const [waitingSnapshot, calledSnapshot] = await Promise.all([
        getDocs(waitingQuery),
        getDocs(calledQuery)
      ]);

      const totalWaiting = waitingSnapshot.size;
      const totalCalled = calledSnapshot.size;

      // Obtener el número actual más alto
      const allQuery = query(
        queueCollection,
        where('barId', '==', barId),
        orderBy('queueNumber', 'desc')
      );
      
      const allSnapshot = await getDocs(allQuery);
      const currentNumber = allSnapshot.empty ? 0 : allSnapshot.docs[0].data()['queueNumber'];

      return {
        totalWaiting,
        totalCalled,
        averageWaitTime: 15, // Placeholder - calcular basado en datos históricos
        currentNumber
      };
    } catch (error) {
      console.error('Error getting queue stats:', error);
      throw error;
    }
  }

  private async getNextQueueNumber(barId: string): Promise<number> {
    try {
      const queueCollection = collection(this.firestore, 'queue');
      const q = query(
        queueCollection,
        where('barId', '==', barId),
        orderBy('queueNumber', 'desc')
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return 1;
      }

      const lastEntry = snapshot.docs[0].data();
      return lastEntry['queueNumber'] + 1;
    } catch (error) {
      console.error('Error getting next queue number:', error);
      return 1;
    }
  }
}
