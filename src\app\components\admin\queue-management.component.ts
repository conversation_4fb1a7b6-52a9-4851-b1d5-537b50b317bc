import { Component, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { QueueService } from '../../services/queue.service';
import { BarService } from '../../services/bar.service';
import { QueueEntry, QueueStatus } from '../../models/queue-entry.model';
import { Bar } from '../../models/bar.model';

@Component({
  selector: 'app-queue-management',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatChipsModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  template: `
    <mat-toolbar>
      <button mat-icon-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <span>{{ bar?.name }} - Gestión de Cola</span>
      <span class="spacer"></span>
      <mat-chip-set>
        <mat-chip>En espera: {{ waitingCount }}</mat-chip>
        <mat-chip>Llamados: {{ calledCount }}</mat-chip>
      </mat-chip-set>
    </mat-toolbar>

    <div class="container">
      <div class="queue-stats" *ngIf="bar">
        <mat-card>
          <mat-card-content>
            <div class="stats-grid">
              <div class="stat-item">
                <mat-icon>people</mat-icon>
                <div>
                  <span class="stat-number">{{ queueEntries.length }}</span>
                  <span class="stat-label">Total en cola</span>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon>schedule</mat-icon>
                <div>
                  <span class="stat-number">15</span>
                  <span class="stat-label">Min promedio</span>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon>trending_up</mat-icon>
                <div>
                  <span class="stat-number">{{ currentNumber }}</span>
                  <span class="stat-label">Número actual</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="queue-list">
        <h2>Cola Actual</h2>
        
        <div *ngIf="queueEntries.length === 0" class="empty-queue">
          <mat-card>
            <mat-card-content>
              <div class="empty-content">
                <mat-icon class="empty-icon">queue</mat-icon>
                <h3>No hay personas en la cola</h3>
                <p>Los clientes aparecerán aquí cuando se unan a la cola</p>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <mat-card class="queue-entry-card" *ngFor="let entry of queueEntries; trackBy: trackByEntryId">
          <mat-card-content>
            <div class="entry-header">
              <div class="entry-number">
                #{{ entry.queueNumber }}
              </div>
              <div class="entry-status">
                <mat-chip [class]="getStatusClass(entry.status)">
                  {{ getStatusText(entry.status) }}
                </mat-chip>
              </div>
            </div>
            
            <div class="entry-details">
              <div class="customer-info">
                <h4>{{ entry.customerName }}</h4>
                <p><mat-icon>people</mat-icon> {{ entry.partySize }} personas</p>
                <p><mat-icon>phone</mat-icon> {{ entry.phoneNumber }}</p>
                <p><mat-icon>schedule</mat-icon> {{ getWaitTime(entry) }}</p>
              </div>
            </div>
          </mat-card-content>
          
          <mat-card-actions>
            <div class="entry-actions">
              <button 
                mat-raised-button 
                color="primary"
                *ngIf="entry.status === 'waiting'"
                (click)="callCustomer(entry)">
                <mat-icon>call</mat-icon>
                Llamar
              </button>
              
              <button 
                mat-raised-button 
                color="accent"
                *ngIf="entry.status === 'called'"
                (click)="completeEntry(entry)">
                <mat-icon>check</mat-icon>
                Completar
              </button>
              
              <button 
                mat-button 
                color="warn"
                (click)="removeFromQueue(entry)">
                <mat-icon>delete</mat-icon>
                Eliminar
              </button>
            </div>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .spacer {
      flex: 1 1 auto;
    }
    
    .queue-stats {
      margin: 20px 0;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      padding: 20px;
    }
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .stat-item mat-icon {
      color: #3f51b5;
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
    
    .stat-number {
      display: block;
      font-size: 1.5rem;
      font-weight: bold;
      color: #3f51b5;
    }
    
    .stat-label {
      display: block;
      font-size: 0.9rem;
      color: #666;
    }
    
    .queue-list h2 {
      color: #3f51b5;
      margin: 20px 0;
    }
    
    .queue-entry-card {
      margin: 16px 0;
      transition: transform 0.2s ease-in-out;
    }
    
    .queue-entry-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.12);
    }
    
    .entry-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .entry-number {
      font-size: 2rem;
      font-weight: bold;
      color: #3f51b5;
    }
    
    .entry-details {
      margin: 16px 0;
    }
    
    .customer-info h4 {
      margin: 0 0 8px 0;
      color: #333;
    }
    
    .customer-info p {
      display: flex;
      align-items: center;
      margin: 4px 0;
      color: #666;
    }
    
    .customer-info mat-icon {
      margin-right: 8px;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
    
    .entry-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
    
    .status-waiting {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .status-called {
      background-color: #d4edda;
      color: #155724;
    }
    
    .empty-queue {
      margin: 40px 0;
    }
    
    .empty-content {
      text-align: center;
      padding: 40px;
    }
    
    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    .empty-content h3 {
      color: #666;
      margin: 20px 0 10px 0;
    }
    
    .empty-content p {
      color: #999;
    }
  `]
})
export class QueueManagementComponent implements OnInit, OnDestroy {
  bar: Bar | null = null;
  queueEntries: QueueEntry[] = [];
  barId: string = '';
  waitingCount = 0;
  calledCount = 0;
  currentNumber = 0;
  private queueSubscription?: Subscription;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private queueService: QueueService,
    private barService: BarService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  async ngOnInit(): Promise<void> {
    this.barId = this.route.snapshot.paramMap.get('barId') || '';
    
    if (this.barId) {
      await this.loadBar();
      this.subscribeToQueue();
    }
  }

  ngOnDestroy(): void {
    if (this.queueSubscription) {
      this.queueSubscription.unsubscribe();
    }
  }

  private async loadBar(): Promise<void> {
    try {
      this.bar = await this.barService.getBar(this.barId);
    } catch (error) {
      console.error('Error loading bar:', error);
    }
  }

  private subscribeToQueue(): void {
    this.queueSubscription = this.queueService.getQueueForBar(this.barId).subscribe(
      entries => {
        this.queueEntries = entries;
        this.updateCounts();
      }
    );
  }

  private updateCounts(): void {
    this.waitingCount = this.queueEntries.filter(e => e.status === QueueStatus.WAITING).length;
    this.calledCount = this.queueEntries.filter(e => e.status === QueueStatus.CALLED).length;
    this.currentNumber = this.queueEntries.length > 0 ? 
      Math.max(...this.queueEntries.map(e => e.queueNumber)) : 0;
  }

  async callCustomer(entry: QueueEntry): Promise<void> {
    try {
      await this.queueService.callCustomer(entry.id);
      this.snackBar.open(`${entry.customerName} ha sido llamado`, 'Cerrar', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error calling customer:', error);
      this.snackBar.open('Error al llamar al cliente', 'Cerrar', {
        duration: 3000
      });
    }
  }

  async completeEntry(entry: QueueEntry): Promise<void> {
    try {
      await this.queueService.completeEntry(entry.id);
      this.snackBar.open(`Mesa para ${entry.customerName} completada`, 'Cerrar', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error completing entry:', error);
      this.snackBar.open('Error al completar la entrada', 'Cerrar', {
        duration: 3000
      });
    }
  }

  async removeFromQueue(entry: QueueEntry): Promise<void> {
    try {
      await this.queueService.removeFromQueue(entry.id);
      this.snackBar.open(`${entry.customerName} eliminado de la cola`, 'Cerrar', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error removing from queue:', error);
      this.snackBar.open('Error al eliminar de la cola', 'Cerrar', {
        duration: 3000
      });
    }
  }

  getStatusClass(status: QueueStatus): string {
    switch (status) {
      case QueueStatus.WAITING:
        return 'status-waiting';
      case QueueStatus.CALLED:
        return 'status-called';
      default:
        return '';
    }
  }

  getStatusText(status: QueueStatus): string {
    switch (status) {
      case QueueStatus.WAITING:
        return 'Esperando';
      case QueueStatus.CALLED:
        return 'Llamado';
      default:
        return 'Desconocido';
    }
  }

  getWaitTime(entry: QueueEntry): string {
    const now = new Date();
    const created = new Date(entry.createdAt);
    const diffMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60));
    return `${diffMinutes} min esperando`;
  }

  trackByEntryId(index: number, entry: QueueEntry): string {
    return entry.id;
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }
}
