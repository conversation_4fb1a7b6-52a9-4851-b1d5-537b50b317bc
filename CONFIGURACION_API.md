# Configuración de la API para QueTeToca

## Cambios realizados en el proyecto Angular

### 1. Nuevos servicios creados

#### ApiService (`src/app/services/api.service.ts`)
- Servicio centralizado para todas las llamadas HTTP a la API
- Manejo automático de tokens de Firebase
- Métodos para todos los endpoints de la API
- Manejo de errores HTTP

#### WebSocketService (`src/app/services/websocket.service.ts`)
- Conexión WebSocket para actualizaciones en tiempo real
- Reconexión automática en caso de desconexión
- Suscripciones específicas para colas y entradas

#### ErrorInterceptor (`src/app/interceptors/error.interceptor.ts`)
- Interceptor para manejo centralizado de errores HTTP
- Mensajes de error amigables para el usuario
- Redirección automática en caso de sesión expirada

### 2. Servicios actualizados

#### AuthService
- Ahora usa la API en lugar de Firestore directamente
- Verificación de tokens con el backend
- Obtención de datos de usuario desde la API

#### QueueService
- Integración con WebSockets para tiempo real
- Todas las operaciones ahora van a través de la API
- Eliminación de dependencias directas de Firestore

#### BarService
- Migración completa a la API
- Eliminación de lógica de Firestore
- Simplificación de métodos

### 3. Configuración actualizada

#### Environments
- Agregada configuración de API y WebSocket
- URLs separadas para desarrollo y producción
- Endpoints organizados por módulo

#### App Config
- Agregado HttpClient provider
- Eliminado Firestore provider (ya no se usa directamente)
- Configuración de interceptores

#### Package.json
- Script de desarrollo con proxy configurado
- Nuevo script sin proxy para casos especiales

### 4. Archivos de configuración

#### proxy.conf.json
- Configuración de proxy para desarrollo
- Redirección de llamadas API al backend local
- Soporte para WebSockets

## Configuración de Firebase

### Cambios necesarios en Firebase Console

1. **Authentication**
   - Mantener configuración de Google OAuth
   - Agregar dominios autorizados para la API

2. **Firestore** (Opcional)
   - Ya no se usa directamente desde Angular
   - Solo la API accede a Firestore
   - Mantener reglas básicas de seguridad

## Configuración de la API Python

### Variables de entorno necesarias

Crear archivo `.env` en el proyecto de la API:

```env
# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/quetetoca
# O para SQLite en desarrollo:
# DATABASE_URL=sqlite:///./quetetoca.db

# API Configuration
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_ORIGINS=http://localhost:4200,https://your-domain.com

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Environment
ENVIRONMENT=development
```

### Configuración de Firebase Admin SDK

1. **Generar clave de servicio**
   - Ve a Firebase Console > Configuración del proyecto
   - Pestaña "Cuentas de servicio"
   - Generar nueva clave privada
   - Descargar archivo JSON

2. **Configurar variables de entorno**
   - Extraer valores del archivo JSON
   - Configurar en el archivo `.env`

## Flujo de desarrollo

### 1. Desarrollo local

```bash
# Terminal 1: Iniciar API Python
cd api/
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Iniciar Angular con proxy
cd QueTeToca/
npm start
```

### 2. URLs de desarrollo
- Frontend: `http://localhost:4200`
- API: `http://localhost:8000`
- Documentación API: `http://localhost:8000/docs`
- WebSocket: `ws://localhost:8000/ws`

### 3. Flujo de autenticación

1. Usuario hace login con Google en Angular
2. Angular obtiene token de Firebase
3. Angular envía token a la API en header Authorization
4. API verifica token con Firebase Admin SDK
5. API retorna datos del usuario o error

### 4. Flujo de tiempo real

1. Angular se conecta al WebSocket
2. Se suscribe a actualizaciones de cola específica
3. API envía actualizaciones cuando hay cambios
4. Angular actualiza la UI automáticamente

## Testing

### Probar la integración

1. **Verificar conexión a la API**
   ```bash
   curl http://localhost:8000/health
   ```

2. **Probar autenticación**
   - Hacer login en Angular
   - Verificar que aparezcan requests en la API
   - Comprobar logs de autenticación

3. **Probar WebSocket**
   - Abrir múltiples pestañas del admin
   - Hacer cambios en una pestaña
   - Verificar actualizaciones en tiempo real

### Debugging

#### Problemas comunes

1. **CORS errors**
   - Verificar configuración de ALLOWED_ORIGINS
   - Comprobar que la API esté corriendo

2. **Token inválido**
   - Verificar configuración de Firebase Admin SDK
   - Comprobar que las credenciales sean correctas

3. **WebSocket no conecta**
   - Verificar que el proxy esté configurado
   - Comprobar logs de la API

#### Logs útiles

```bash
# Ver logs de la API
tail -f api/logs/app.log

# Ver requests HTTP en Angular
# Abrir DevTools > Network tab

# Ver conexiones WebSocket
# DevTools > Network > WS tab
```

## Despliegue

### Producción

1. **Configurar variables de entorno de producción**
2. **Actualizar URLs en environment.prod.ts**
3. **Configurar HTTPS para WebSockets (WSS)**
4. **Configurar CORS para dominio de producción**

### Docker

```dockerfile
# Dockerfile para la API
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  api:
    build: ./api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/quetetoca
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=quetetoca
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## Próximos pasos

1. **Implementar la API con el prompt proporcionado**
2. **Configurar Firebase Admin SDK**
3. **Probar la integración completa**
4. **Implementar tests unitarios**
5. **Configurar CI/CD**
6. **Desplegar a producción**
