# Prompt para API FastAPI con Autenticación JWT

Crea una API REST completa con FastAPI y Python para la aplicación QueTeToca de gestión de colas en bares. La API debe incluir:

## MODELOS DE DATOS:

### User
```python
- id: int (Primary Key)
- email: str (Unique)
- password_hash: str
- first_name: str
- last_name: str
- role: enum ('admin', 'user')
- company_id: int (Foreign Key, nullable)
- is_active: bool
- created_at: datetime
- updated_at: datetime
```

### Company
```python
- id: int (Primary Key)
- name: str
- created_at: datetime
- updated_at: datetime
```

### Bar
```python
- id: int (Primary Key)
- name: str
- address: str
- company_id: int (Foreign Key)
- qr_code: str (nullable)
- is_active: bool
- created_at: datetime
- updated_at: datetime
```

### QueueEntry
```python
- id: int (Primary Key)
- bar_id: int (Foreign Key)
- customer_name: str
- phone_number: str
- party_size: int
- queue_number: int
- status: enum ('waiting', 'called', 'completed', 'cancelled')
- created_at: datetime
- called_at: datetime (nullable)
- completed_at: datetime (nullable)
```

## ENDPOINTS REQUERIDOS:

### AUTH:
- POST /auth/register - Registrar nuevo usuario
- POST /auth/login - Iniciar sesión (retorna JWT)
- POST /auth/logout - Cerrar sesión
- POST /auth/refresh - Renovar token JWT
- GET /auth/me - Obtener usuario actual

### BARS:
- GET /bars - Listar bares (filtrar por company_id si es admin)
- POST /bars - Crear nuevo bar
- GET /bars/{bar_id} - Obtener bar específico
- PUT /bars/{bar_id} - Actualizar bar
- DELETE /bars/{bar_id} - Eliminar bar
- POST /bars/{bar_id}/qr - Generar código QR

### QUEUE:
- GET /bars/{bar_id}/queue - Obtener cola de un bar
- POST /bars/{bar_id}/queue - Unirse a la cola
- PUT /queue/{entry_id}/call - Llamar cliente
- PUT /queue/{entry_id}/complete - Completar servicio
- DELETE /queue/{entry_id} - Eliminar de la cola
- GET /queue/{entry_id} - Obtener estado específico de cola

### STATS:
- GET /bars/{bar_id}/stats - Estadísticas de la cola
- GET /companies/{company_id}/stats - Estadísticas de la empresa

### WEBSOCKETS:
- /ws - Endpoint WebSocket para actualizaciones en tiempo real
- Suscripciones por bar_id
- Notificaciones de cambios en cola

## CARACTERÍSTICAS TÉCNICAS:

### Autenticación y Seguridad:
- JWT tokens con refresh tokens
- Hashing de contraseñas con bcrypt
- Middleware de autenticación
- Validación de permisos por rol
- Rate limiting para endpoints públicos

### Base de Datos:
- SQLAlchemy ORM
- Alembic para migraciones
- PostgreSQL para producción
- SQLite para desarrollo
- Modelos con relaciones apropiadas

### WebSockets:
- FastAPI WebSocket support
- Gestión de conexiones por bar
- Broadcast de actualizaciones en tiempo real
- Manejo de desconexiones

### Validación y Serialización:
- Pydantic models para request/response
- Validación automática de datos
- Serialización JSON optimizada
- Manejo de errores de validación

### Configuración:
- Variables de entorno con pydantic-settings
- Configuración por ambiente (dev/prod)
- Logging estructurado
- Configuración de CORS

### Funcionalidades Adicionales:
- Generación de códigos QR con qrcode
- Paginación para listas grandes
- Filtros y búsqueda
- Soft delete para registros importantes

## ESTRUCTURA DE PROYECTO:
```
api/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── config.py
│   ├── database.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── company.py
│   │   ├── bar.py
│   │   └── queue.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── auth.py
│   │   ├── bar.py
│   │   └── queue.py
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── bars.py
│   │   ├── queue.py
│   │   └── websocket.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── bar.py
│   │   ├── queue.py
│   │   └── websocket.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   └── cors.py
│   └── utils/
│       ├── __init__.py
│       ├── security.py
│       ├── qr_generator.py
│       └── dependencies.py
├── alembic/
├── tests/
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── .env.example
```

## ESQUEMAS PYDANTIC PRINCIPALES:

### Auth Schemas:
```python
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    company_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class UserResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    company_id: Optional[int]
    is_active: bool
```

### Queue Schemas:
```python
class QueueEntryCreate(BaseModel):
    customer_name: str
    phone_number: str
    party_size: int

class QueueEntryResponse(BaseModel):
    id: int
    bar_id: int
    customer_name: str
    phone_number: str
    party_size: int
    queue_number: int
    status: str
    created_at: datetime
```

## FUNCIONALIDADES ESPECÍFICAS:

### Sistema de Colas:
- Numeración automática secuencial por bar
- Estados de cola con transiciones válidas
- Tiempo estimado de espera
- Estadísticas en tiempo real

### WebSocket Features:
- Conexiones agrupadas por bar
- Notificaciones push para cambios de estado
- Heartbeat para mantener conexiones activas
- Reconexión automática del cliente

### Seguridad:
- Tokens JWT con expiración
- Refresh tokens para renovación automática
- Validación de permisos por endpoint
- Sanitización de inputs

### Performance:
- Queries optimizadas con eager loading
- Índices de base de datos apropiados
- Caché para datos frecuentemente accedidos
- Paginación eficiente

Incluye documentación automática con Swagger, tests unitarios con pytest, manejo de errores HTTP apropiados, logging estructurado, y configuración para Docker.
