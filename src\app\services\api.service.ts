import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Auth, user } from '@angular/fire/auth';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = environment.api.baseUrl;
  private tokenSubject = new BehaviorSubject<string | null>(null);

  constructor(
    private http: HttpClient,
    private auth: Auth
  ) {
    // Escuchar cambios en el token de Firebase
    user(this.auth).subscribe(async (firebaseUser) => {
      if (firebaseUser) {
        const token = await firebaseUser.getIdToken();
        this.tokenSubject.next(token);
      } else {
        this.tokenSubject.next(null);
      }
    });
  }

  private getHeaders(): Observable<HttpHeaders> {
    return this.tokenSubject.pipe(
      map(token => {
        let headers = new HttpHeaders({
          'Content-Type': 'application/json'
        });
        
        if (token) {
          headers = headers.set('Authorization', `Bearer ${token}`);
        }
        
        return headers;
      })
    );
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Ocurrió un error desconocido';
    
    if (error.error instanceof ErrorEvent) {
      // Error del lado del cliente
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Error del lado del servidor
      switch (error.status) {
        case 400:
          errorMessage = 'Solicitud inválida';
          break;
        case 401:
          errorMessage = 'No autorizado';
          break;
        case 403:
          errorMessage = 'Acceso prohibido';
          break;
        case 404:
          errorMessage = 'Recurso no encontrado';
          break;
        case 500:
          errorMessage = 'Error interno del servidor';
          break;
        default:
          errorMessage = `Error ${error.status}: ${error.message}`;
      }
    }
    
    console.error('API Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  // Métodos genéricos HTTP
  get<T>(endpoint: string): Observable<T> {
    return this.getHeaders().pipe(
      switchMap(headers => 
        this.http.get<T>(`${this.baseUrl}${endpoint}`, { headers })
          .pipe(catchError(this.handleError))
      )
    );
  }

  post<T>(endpoint: string, data: any): Observable<T> {
    return this.getHeaders().pipe(
      switchMap(headers => 
        this.http.post<T>(`${this.baseUrl}${endpoint}`, data, { headers })
          .pipe(catchError(this.handleError))
      )
    );
  }

  put<T>(endpoint: string, data: any): Observable<T> {
    return this.getHeaders().pipe(
      switchMap(headers => 
        this.http.put<T>(`${this.baseUrl}${endpoint}`, data, { headers })
          .pipe(catchError(this.handleError))
      )
    );
  }

  delete<T>(endpoint: string): Observable<T> {
    return this.getHeaders().pipe(
      switchMap(headers => 
        this.http.delete<T>(`${this.baseUrl}${endpoint}`, { headers })
          .pipe(catchError(this.handleError))
      )
    );
  }

  // Métodos específicos de la API

  // Auth
  verifyToken(): Observable<any> {
    return this.post('/auth/verify-token', {});
  }

  getCurrentUser(): Observable<any> {
    return this.get('/auth/me');
  }

  // Bars
  getBars(companyId?: string): Observable<any[]> {
    const endpoint = companyId ? `/bars?company_id=${companyId}` : '/bars';
    return this.get(endpoint);
  }

  getBar(barId: string): Observable<any> {
    return this.get(`/bars/${barId}`);
  }

  createBar(barData: any): Observable<any> {
    return this.post('/bars', barData);
  }

  updateBar(barId: string, barData: any): Observable<any> {
    return this.put(`/bars/${barId}`, barData);
  }

  deleteBar(barId: string): Observable<any> {
    return this.delete(`/bars/${barId}`);
  }

  generateQR(barId: string): Observable<any> {
    return this.post(`/bars/${barId}/qr`, {});
  }

  // Queue
  getQueue(barId: string): Observable<any[]> {
    return this.get(`/bars/${barId}/queue`);
  }

  joinQueue(barId: string, queueData: any): Observable<any> {
    return this.post(`/bars/${barId}/queue`, queueData);
  }

  getQueueEntry(entryId: string): Observable<any> {
    return this.get(`/queue/${entryId}`);
  }

  callCustomer(entryId: string): Observable<any> {
    return this.put(`/queue/${entryId}/call`, {});
  }

  completeEntry(entryId: string): Observable<any> {
    return this.put(`/queue/${entryId}/complete`, {});
  }

  removeFromQueue(entryId: string): Observable<any> {
    return this.delete(`/queue/${entryId}`);
  }

  // Stats
  getBarStats(barId: string): Observable<any> {
    return this.get(`/bars/${barId}/stats`);
  }

  getCompanyStats(companyId: string): Observable<any> {
    return this.get(`/companies/${companyId}/stats`);
  }
}
