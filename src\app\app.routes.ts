import { Routes } from '@angular/router';
import { AuthGuard, AdminGuard } from './guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./components/auth/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'queue/:barId',
    loadComponent: () => import('./components/queue/queue-form.component').then(m => m.QueueFormComponent)
  },
  {
    path: 'queue-status/:entryId',
    loadComponent: () => import('./components/queue/queue-status.component').then(m => m.QueueStatusComponent)
  },
  {
    path: 'admin',
    canActivate: [AuthGuard, AdminGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./components/admin/admin-dashboard.component').then(m => m.AdminDashboardComponent)
      },
      {
        path: 'users',
        loadComponent: () => import('./components/admin/user-management.component').then(m => m.UserManagementComponent)
      },
      {
        path: 'queue/:barId',
        loadComponent: () => import('./components/admin/queue-management.component').then(m => m.QueueManagementComponent)
      },
      {
        path: 'qr/:barId',
        loadComponent: () => import('./components/admin/qr-display.component').then(m => m.QrDisplayComponent)
      }
    ]
  },
  {
    path: '**',
    redirectTo: '/login'
  }
];
