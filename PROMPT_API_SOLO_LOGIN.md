# Prompt para API FastAPI - Solo Login + Gestión de Usuarios Admin

Crea una API REST completa con FastAPI y Python para la aplicación QueTeToca de gestión de colas en bares. **IMPORTANTE**: No hay registro público, solo login y gestión de usuarios desde el panel de administrador.

## MODELOS DE DATOS:

### User
```python
- id: int (Primary Key)
- email: str (Unique)
- password_hash: str
- first_name: str
- last_name: str
- role: enum ('admin', 'user') 
- company_id: int (Foreign Key, nullable)
- is_active: bool (default: True)
- created_at: datetime
- updated_at: datetime
- created_by: int (Foreign Key to User, nullable) # Quién creó este usuario
```

### Company
```python
- id: int (Primary Key)
- name: str
- created_at: datetime
- updated_at: datetime
```

### Bar
```python
- id: int (Primary Key)
- name: str
- address: str
- company_id: int (Foreign Key)
- qr_code: str (nullable)
- is_active: bool (default: True)
- created_at: datetime
- updated_at: datetime
```

### QueueEntry
```python
- id: int (Primary Key)
- bar_id: int (Foreign Key)
- customer_name: str
- phone_number: str
- party_size: int
- queue_number: int
- status: enum ('waiting', 'called', 'completed', 'cancelled')
- created_at: datetime
- called_at: datetime (nullable)
- completed_at: datetime (nullable)
```

## ENDPOINTS REQUERIDOS:

### AUTH (Solo Login):
- **POST /auth/login** - Iniciar sesión (retorna JWT + refresh token)
- **POST /auth/logout** - Cerrar sesión
- **POST /auth/refresh** - Renovar token JWT
- **GET /auth/me** - Obtener usuario actual

### ADMIN - GESTIÓN DE USUARIOS:
- **GET /admin/users** - Listar todos los usuarios (solo admins)
- **POST /admin/users** - Crear nuevo usuario (solo admins)
- **PUT /admin/users/{user_id}** - Actualizar usuario (solo admins)
- **DELETE /admin/users/{user_id}** - Eliminar usuario (solo admins)
- **PUT /admin/users/{user_id}/toggle-status** - Activar/desactivar usuario

### BARS:
- **GET /bars** - Listar bares (filtrar por company_id del usuario)
- **POST /bars** - Crear nuevo bar (solo admins)
- **GET /bars/{bar_id}** - Obtener bar específico
- **PUT /bars/{bar_id}** - Actualizar bar (solo admins)
- **DELETE /bars/{bar_id}** - Eliminar bar (solo admins)
- **POST /bars/{bar_id}/qr** - Generar código QR

### QUEUE:
- **GET /bars/{bar_id}/queue** - Obtener cola de un bar
- **POST /bars/{bar_id}/queue** - Unirse a la cola (público)
- **PUT /queue/{entry_id}/call** - Llamar cliente (solo admins)
- **PUT /queue/{entry_id}/complete** - Completar servicio (solo admins)
- **DELETE /queue/{entry_id}** - Eliminar de la cola (solo admins)
- **GET /queue/{entry_id}** - Obtener estado específico de cola (público)

### STATS:
- **GET /bars/{bar_id}/stats** - Estadísticas de la cola
- **GET /companies/{company_id}/stats** - Estadísticas de la empresa (solo admins)

### WEBSOCKETS:
- **/ws** - Endpoint WebSocket para actualizaciones en tiempo real
- Suscripciones por bar_id
- Notificaciones de cambios en cola

## CARACTERÍSTICAS TÉCNICAS:

### Autenticación y Seguridad:
- **JWT tokens** con refresh tokens
- **Hashing de contraseñas** con bcrypt
- **Middleware de autenticación** para rutas protegidas
- **Validación de permisos por rol** (admin vs user)
- **Rate limiting** para endpoints de login
- **Usuario administrador inicial** creado automáticamente

### Permisos por Rol:
```python
# Solo ADMIN puede:
- Crear, editar, eliminar usuarios
- Crear, editar, eliminar bares
- Ver estadísticas de empresa
- Gestionar colas (llamar, completar, eliminar)

# USER puede:
- Ver bares de su empresa
- Ver colas de bares de su empresa
- Gestionar colas básicamente (solo lectura por defecto)

# PÚBLICO puede:
- Unirse a colas
- Ver estado de su entrada en cola
```

### Base de Datos:
- **SQLAlchemy ORM** con relaciones apropiadas
- **Alembic** para migraciones
- **PostgreSQL** para producción
- **SQLite** para desarrollo
- **Índices** optimizados para consultas frecuentes

### Inicialización:
```python
# Crear usuario administrador por defecto:
email: <EMAIL>
password: admin123 (cambiar en primer login)
role: admin
first_name: Administrador
last_name: Principal
```

### WebSockets:
- **FastAPI WebSocket** support
- **Gestión de conexiones** por bar
- **Broadcast** de actualizaciones en tiempo real
- **Manejo de desconexiones** automático

### Validación y Serialización:
```python
# Schemas principales:

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserCreate(BaseModel):  # Solo para admins
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    role: Literal['admin', 'user'] = 'user'
    company_name: Optional[str] = None

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[Literal['admin', 'user']] = None
    is_active: Optional[bool] = None

class UserResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    company_id: Optional[int]
    is_active: bool
    created_at: datetime

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: UserResponse
```

### Configuración:
- **Variables de entorno** con pydantic-settings
- **Configuración por ambiente** (dev/prod)
- **Logging estructurado** con rotación
- **Configuración de CORS** para frontend

### Funcionalidades Adicionales:
- **Generación de códigos QR** con qrcode library
- **Paginación** para listas grandes
- **Filtros y búsqueda** en usuarios y bares
- **Soft delete** para registros importantes
- **Auditoría** de acciones administrativas

## ESTRUCTURA DE PROYECTO:
```
api/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── config.py
│   ├── database.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── company.py
│   │   ├── bar.py
│   │   └── queue.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── auth.py
│   │   ├── bar.py
│   │   └── queue.py
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── admin.py      # Gestión de usuarios
│   │   ├── bars.py
│   │   ├── queue.py
│   │   └── websocket.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── user.py       # Gestión de usuarios
│   │   ├── bar.py
│   │   ├── queue.py
│   │   └── websocket.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   └── cors.py
│   └── utils/
│       ├── __init__.py
│       ├── security.py
│       ├── qr_generator.py
│       ├── dependencies.py
│       └── init_db.py    # Crear admin inicial
├── alembic/
├── tests/
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── .env.example
```

## FUNCIONALIDADES ESPECÍFICAS:

### Sistema de Autenticación:
- **Solo login**, no registro público
- **Tokens JWT** con expiración de 30 minutos
- **Refresh tokens** con expiración de 7 días
- **Logout** que invalida tokens
- **Protección contra ataques** de fuerza bruta

### Gestión de Usuarios (Solo Admins):
- **CRUD completo** de usuarios
- **Asignación de roles** y empresas
- **Activación/desactivación** de cuentas
- **Auditoría** de creación de usuarios
- **Validación** de emails únicos

### Sistema de Colas:
- **Numeración automática** secuencial por bar
- **Estados de cola** con transiciones válidas
- **Tiempo estimado** de espera
- **Estadísticas** en tiempo real
- **Notificaciones** WebSocket

### Seguridad:
- **Validación de permisos** en cada endpoint
- **Sanitización** de inputs
- **Rate limiting** en login
- **Logs de seguridad** para acciones administrativas
- **Encriptación** de contraseñas con bcrypt

### Performance:
- **Queries optimizadas** con eager loading
- **Índices de base de datos** apropiados
- **Paginación** eficiente
- **Caché** para datos frecuentemente accedidos

Incluye documentación automática con Swagger, tests unitarios con pytest, manejo de errores HTTP apropiados, logging estructurado, y configuración para Docker.

## COMANDOS DE INICIALIZACIÓN:

```bash
# Crear usuario administrador inicial
python -m app.utils.init_db

# Ejecutar migraciones
alembic upgrade head

# Iniciar servidor
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```
