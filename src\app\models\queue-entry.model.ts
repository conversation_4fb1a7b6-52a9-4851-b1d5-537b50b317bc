export interface QueueEntry {
  id: string;
  barId: string;
  customerName: string;
  phoneNumber: string;
  partySize: number;
  queueNumber: number;
  status: QueueStatus;
  createdAt: Date;
  calledAt?: Date;
  completedAt?: Date;
  estimatedWaitTime?: number;
}

export enum QueueStatus {
  WAITING = 'waiting',
  CALLED = 'called',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface QueueStats {
  totalWaiting: number;
  totalCalled: number;
  averageWaitTime: number;
  currentNumber: number;
}
