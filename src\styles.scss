/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.form-container {
  max-width: 500px;
  width: 100%;
  padding: 20px;
}

.queue-card {
  margin: 10px 0;
  padding: 15px;
}

.queue-number {
  font-size: 2rem;
  font-weight: bold;
  color: #3f51b5;
  text-align: center;
  margin: 20px 0;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-waiting {
  background-color: #fff3cd;
  color: #856404;
}

.status-called {
  background-color: #d4edda;
  color: #155724;
}

.admin-toolbar {
  margin-bottom: 20px;
}

.queue-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
