# Configuración de Firebase para QueTeToca

## Pasos para configurar Firebase

### 1. Crear proyecto en Firebase Console

1. Ve a [Firebase Console](https://console.firebase.google.com)
2. Haz clic en "Crear un proyecto"
3. Nombra tu proyecto (ej: "que-te-toca-prod")
4. Habilita Google Analytics (opcional)
5. Crea el proyecto

### 2. Configurar Authentication

1. En el panel izquierdo, ve a "Authentication"
2. Haz clic en "Comenzar"
3. Ve a la pestaña "Sign-in method"
4. Habilita "Google" como proveedor
5. Configura el nombre público del proyecto
6. Agrega tu email como usuario autorizado

### 3. Configurar Firestore Database

1. En el panel izquierdo, ve a "Firestore Database"
2. Haz clic en "Crear base de datos"
3. Selecciona "Comenzar en modo de prueba" (cambiaremos las reglas después)
4. Elige una ubicación cercana a tus usuarios

### 4. Configurar Hosting (opcional)

1. En el panel izquierdo, ve a "Hosting"
2. Haz clic en "Comenzar"
3. Sigue las instrucciones para instalar Firebase CLI

### 5. Obtener configuración del proyecto

1. Ve a "Configuración del proyecto" (ícono de engranaje)
2. Baja hasta "Tus apps"
3. Haz clic en "Agregar app" y selecciona "Web"
4. Registra tu app con un nombre
5. Copia la configuración que aparece

### 6. Configurar la aplicación

Edita los archivos de configuración con los datos de tu proyecto:

#### `src/environments/environment.ts`
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "tu-api-key",
    authDomain: "tu-proyecto.firebaseapp.com",
    projectId: "tu-proyecto-id",
    storageBucket: "tu-proyecto.appspot.com",
    messagingSenderId: "tu-sender-id",
    appId: "tu-app-id"
  }
};
```

#### `src/environments/environment.prod.ts`
```typescript
export const environment = {
  production: true,
  firebase: {
    apiKey: "tu-api-key",
    authDomain: "tu-proyecto.firebaseapp.com",
    projectId: "tu-proyecto-id",
    storageBucket: "tu-proyecto.appspot.com",
    messagingSenderId: "tu-sender-id",
    appId: "tu-app-id"
  }
};
```

### 7. Configurar reglas de seguridad

1. Ve a "Firestore Database" > "Reglas"
2. Reemplaza las reglas con el contenido del archivo `firestore.rules`
3. Publica las reglas

### 8. Configurar índices

1. Ve a "Firestore Database" > "Índices"
2. Los índices se crearán automáticamente cuando uses la aplicación
3. O puedes importar el archivo `firestore.indexes.json`

### 9. Probar la aplicación

1. Ejecuta `npm start` para iniciar el servidor de desarrollo
2. Ve a `http://localhost:4200`
3. Intenta hacer login con Google
4. Crea un bar de prueba
5. Genera un código QR
6. Prueba el flujo completo

### 10. Desplegar a producción

```bash
# Instalar Firebase CLI
npm install -g firebase-tools

# Inicializar Firebase en el proyecto
firebase init

# Construir la aplicación
npm run build

# Desplegar
firebase deploy
```

## Solución de problemas comunes

### Error de autenticación
- Verifica que el dominio esté autorizado en Firebase Console
- Revisa que las credenciales en environment.ts sean correctas

### Error de permisos en Firestore
- Verifica que las reglas de Firestore estén configuradas correctamente
- Asegúrate de que el usuario esté autenticado

### Error de CORS
- Agrega tu dominio a los dominios autorizados en Firebase Console
- Para desarrollo local, agrega `localhost:4200`

## Configuración adicional recomendada

### Dominios autorizados
En Authentication > Settings > Authorized domains, agrega:
- `localhost` (para desarrollo)
- Tu dominio de producción

### Configuración de email
En Authentication > Templates, personaliza los emails de verificación

### Backup de Firestore
Configura backups automáticos en la consola de Google Cloud

### Monitoreo
Habilita Firebase Performance Monitoring y Crashlytics para producción
