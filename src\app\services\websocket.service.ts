import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface WebSocketMessage {
  type: string;
  data: any;
  barId?: string;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageSubject = new Subject<WebSocketMessage>();
  private connectionStatus = new BehaviorSubject<boolean>(false);
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000; // 5 segundos

  constructor() {}

  connect(barId?: string): Observable<WebSocketMessage> {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return this.messageSubject.asObservable();
    }

    const wsUrl = barId 
      ? `${environment.websocket.url}?bar_id=${barId}`
      : environment.websocket.url;

    this.socket = new WebSocket(wsUrl);

    this.socket.onopen = () => {
      console.log('WebSocket conectado');
      this.connectionStatus.next(true);
      this.reconnectAttempts = 0;
    };

    this.socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.messageSubject.next(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket desconectado:', event.code, event.reason);
      this.connectionStatus.next(false);
      
      if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect(barId);
      }
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.connectionStatus.next(false);
    };

    return this.messageSubject.asObservable();
  }

  private scheduleReconnect(barId?: string): void {
    this.reconnectAttempts++;
    console.log(`Intentando reconectar... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect(barId);
    }, this.reconnectInterval);
  }

  sendMessage(message: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket no está conectado');
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.close(1000, 'Desconexión manual');
      this.socket = null;
    }
  }

  getConnectionStatus(): Observable<boolean> {
    return this.connectionStatus.asObservable();
  }

  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  // Métodos específicos para diferentes tipos de mensajes

  subscribeToQueueUpdates(barId: string): Observable<any> {
    this.connect(barId);
    
    return new Observable(observer => {
      const subscription = this.messageSubject.subscribe(message => {
        if (message.type === 'queue_update' && message.barId === barId) {
          observer.next(message.data);
        }
      });

      // Enviar mensaje de suscripción
      this.sendMessage({
        type: 'subscribe_queue',
        barId: barId
      });

      return () => {
        this.sendMessage({
          type: 'unsubscribe_queue',
          barId: barId
        });
        subscription.unsubscribe();
      };
    });
  }

  subscribeToQueueEntry(entryId: string): Observable<any> {
    this.connect();
    
    return new Observable(observer => {
      const subscription = this.messageSubject.subscribe(message => {
        if (message.type === 'entry_update' && message.data.id === entryId) {
          observer.next(message.data);
        }
      });

      // Enviar mensaje de suscripción
      this.sendMessage({
        type: 'subscribe_entry',
        entryId: entryId
      });

      return () => {
        this.sendMessage({
          type: 'unsubscribe_entry',
          entryId: entryId
        });
        subscription.unsubscribe();
      };
    });
  }

  subscribeToNotifications(): Observable<any> {
    this.connect();
    
    return new Observable(observer => {
      const subscription = this.messageSubject.subscribe(message => {
        if (message.type === 'notification') {
          observer.next(message.data);
        }
      });

      return () => subscription.unsubscribe();
    });
  }
}
