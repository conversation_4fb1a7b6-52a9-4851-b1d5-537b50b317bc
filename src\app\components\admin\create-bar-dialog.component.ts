import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { BarService } from '../../services/bar.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-create-bar-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSnackBarModule
  ],
  template: `
    <h2 mat-dialog-title>Crear Nuevo Bar</h2>
    
    <mat-dialog-content>
      <form [formGroup]="barForm">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nombre del bar</mat-label>
          <input matInput formControlName="name" placeholder="Ej: Bar Central">
          <mat-error *ngIf="barForm.get('name')?.hasError('required')">
            El nombre es requerido
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Dirección</mat-label>
          <textarea matInput formControlName="address" rows="3" placeholder="Dirección completa del bar"></textarea>
          <mat-error *ngIf="barForm.get('address')?.hasError('required')">
            La dirección es requerida
          </mat-error>
        </mat-form-field>
      </form>
    </mat-dialog-content>
    
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancelar</button>
      <button 
        mat-raised-button 
        color="primary" 
        (click)="onCreate()"
        [disabled]="barForm.invalid || isCreating">
        {{ isCreating ? 'Creando...' : 'Crear Bar' }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    mat-dialog-content {
      min-width: 400px;
      padding: 20px 0;
    }
    
    mat-dialog-actions {
      padding: 16px 0;
    }
  `]
})
export class CreateBarDialogComponent {
  barForm: FormGroup;
  isCreating = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CreateBarDialogComponent>,
    private barService: BarService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.barForm = this.fb.group({
      name: ['', [Validators.required]],
      address: ['', [Validators.required]]
    });
  }

  async onCreate(): Promise<void> {
    if (this.barForm.valid) {
      this.isCreating = true;
      
      try {
        const currentUser = await this.authService.getCurrentUser().toPromise();
        
        if (!currentUser?.companyId) {
          // Si no tiene companyId, crear una empresa por defecto
          const companyId = 'default-company'; // Implementar lógica de creación de empresa
          
          const formValue = this.barForm.value;
          await this.barService.createBar(
            formValue.name,
            formValue.address,
            companyId
          );
        } else {
          const formValue = this.barForm.value;
          await this.barService.createBar(
            formValue.name,
            formValue.address,
            currentUser.companyId
          );
        }

        this.snackBar.open('Bar creado exitosamente', 'Cerrar', {
          duration: 3000
        });

        this.dialogRef.close(true);
        
      } catch (error) {
        console.error('Error creating bar:', error);
        this.snackBar.open('Error al crear el bar. Inténtalo de nuevo.', 'Cerrar', {
          duration: 3000
        });
      } finally {
        this.isCreating = false;
      }
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
